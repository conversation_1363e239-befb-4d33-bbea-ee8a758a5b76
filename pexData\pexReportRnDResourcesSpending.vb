﻿Imports Utilities

' Minimal "Module A" for R&D Resources Spending
Public Module pexReportARnDResourcesSpending

    ' ---- Output you will save later ----
    Public lstPexRnDRS As List(Of pexRnDResourcesSpending)

    Public Sub CleanMemory()
        If lstPexRnDRS IsNot Nothing Then
            lstPexRnDRS.Clear()
            lstPexRnDRS = Nothing
        End If
    End Sub

    ' Build a flat list: one pexRnDResourcesSpending per TIMESHEET_ENTRY row
    Public Sub BuildRnDResourcesSpending()
        Using New Chrono(Sub(ms) Log($"BuildRnDResourcesSpending done. Elapsed: {ms:#0}ms"),
                         Sub() Log("BuildRnDResourcesSpending started..."))

            If lstTimesheetEntries Is Nothing OrElse lstTimesheetEntries.Count = 0 Then
                Log("No timesheet entries loaded; lstPexRnDRS will be empty.")
                lstPexRnDRS = New List(Of pexRnDResourcesSpending)
                Exit Sub
            End If

            lstPexRnDRS = New List(Of pexRnDResourcesSpending)(capacity:=lstTimesheetEntries.Count)

            For Each te In lstTimesheetEntries

                ' Employee name (safe lookup)
                Dim empName As String = ""
                Dim emp As employe = Nothing
                If dicEmploye IsNot Nothing AndAlso dicEmploye.TryGetValue(te.id_employe, emp) AndAlso emp IsNot Nothing Then
                    empName = ($"{emp.prenom} {emp.nom}").Trim()
                End If

                ' Date used for rate lookups (wages + FX)
                Dim atDate As DateTime = If(te.date.HasValue, te.date.Value, Now)

                ' Prefer the pre-populated taux_horaire; fallback if needed
                Dim tauxHoraire As Decimal = If(te.taux_horaire.HasValue, te.taux_horaire.Value, 0D)
                If tauxHoraire <= 0D Then
                    Dim rateTuple = GetEmployeRateAt(te.id_employe, atDate)
                    tauxHoraire = rateTuple.TauxHoraire
                End If

                ' Declared vs adjusted hours
                Dim heures As Decimal = te.heures
                Dim heuresAdj As Decimal = If(te.heures_ajustées.HasValue, te.heures_ajustées.Value, te.heures)

                ' Minutes (rounded away from zero)
                Dim workMin As Integer = CInt(Math.Round(heures * 60D, MidpointRounding.AwayFromZero))
                Dim workMinAdj As Integer = CInt(Math.Round(heuresAdj * 60D, MidpointRounding.AwayFromZero))

                ' --- Costs ---
                ' Compute raw CAD amounts first (better precision), then round for storage
                Dim labourCostCadRaw As Decimal = heures * tauxHoraire
                Dim labourCostAdjCadRaw As Decimal = heuresAdj * tauxHoraire

                ' FX: reuse your GetTauxChange(...) + fallback standard rate, guarding to 1.0 when needed
                Dim cadPerUsd As Decimal = GetFxCadPerUsd(atDate) ' CAD per 1 USD

                ' USD from CAD (convert then round)
                Dim labourCostUs As Decimal = Math.Round(Div0(labourCostCadRaw, cadPerUsd), 2, MidpointRounding.AwayFromZero)
                Dim labourCostAdjustedUs As Decimal = Math.Round(Div0(labourCostAdjCadRaw, cadPerUsd), 2, MidpointRounding.AwayFromZero)

                ' Rounded CAD for storage (2 decimals)
                Dim labourCost As Decimal = Math.Round(labourCostCadRaw, 2, MidpointRounding.AwayFromZero)
                Dim labourCostAdjusted As Decimal = Math.Round(labourCostAdjCadRaw, 2, MidpointRounding.AwayFromZero)

                ' Create the row (per timesheet entry)
                Dim row As New pexRnDResourcesSpending With {
                    .EmployeID = te.id_employe,
                    .EmployeeName = empName,
                    .RnDProjectName = te.task_nom,
                    .RnDProjectType = te.task_type_nom,
                    .RnDProjectDescription = te.timesheet_task_description,
                    .WorkTimeMin = workMin,
                    .WorkTimeMinAdjusted = workMinAdj,
                    .LabourCost = labourCost,                               ' CAD (mapped to LabourCostCan)
                    .LabourCostAdjusted = labourCostAdjusted,               ' CAD (mapped to LabourCostAdjustedCan)
                    .LabourCostUs = labourCostUs,                           ' USD
                    .LabourCostAdjustedUs = labourCostAdjustedUs,           ' USD
                    .WorkDate = te.date,
                    .Annee = te.date?.Year,
                    .AnneeFiscale = AnneeFiscale(te.date),
                    .Mois = te.date?.Month,
                    .Jour = te.date?.Day
                }

                lstPexRnDRS.Add(row)
            Next

            Log($"lstPexRnDRS={lstPexRnDRS.Count:0,0}")
        End Using
    End Sub

    Private Function GetFxCadPerUsd(atDate As DateTime) As Decimal
        Dim tc As taux_change = GetTauxChange(atDate) ' your function that walks back day-by-day
        Dim rate As Decimal = If(tc IsNot Nothing AndAlso tc.taux > 0D, tc.taux, GetStdTaux(atDate))
        If rate <= 0D Then rate = 1D
        Return rate
    End Function

    Private Function Div0(n As Decimal, d As Decimal) As Decimal
        If d = 0D Then Return 0D
        Return n / d
    End Function

End Module