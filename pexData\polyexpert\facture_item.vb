﻿Imports ProtoBuf

<PetaPoco.PrimaryKey("ID_FACTURE_ITEM")>
<ProtoContract>
Partial Public Class facture_item
    <ProtoMember(1)> Public Property id_facture_item As Integer
    <ProtoMember(2)> Public Property id_facture As Integer
    'Public Property quantite_commande As Double?
    <ProtoMember(4)> Public Property quantite_expedie As Decimal
    <ProtoMember(5)> Public Property unite As String
    <ProtoMember(6)> Public Property prix As Decimal
    <ProtoMember(7)> Public Property code_produit As String
    'Public Property additif_caracteristique As String
    'Public Property description As String
    <ProtoMember(10)> Public Property id_type_retour As Integer
    'Public Property compte_retour As String
    'Public Property automatique As Integer?
    'Public Property rabais As Boolean?
    'Public Property random_journal As Integer?
    'Public Property id_facture_item_surcharge As Integer?
    'Public Property total_unbound As Decimal?
    'Public Property rowversion As Byte()
    'Public Property compte_vente As String
End Class