﻿Imports System.IO
Imports System.Reflection
Imports System.ServiceModel
Imports Utilities
Imports UtilitiesWeb
Imports UtilitiesData
Imports WSPex

<ServiceBehavior(ConcurrencyMode:=ConcurrencyMode.Single, InstanceContextMode:=InstanceContextMode.PerCall)>
Public Class WSPex : Implements IWSPex

    Private ReadOnly objLock As Object = New Object

    Public Shared ReadOnly WEB_SERVICE_START_TIME As DateTime = Now
    Public Shared ReadOnly Property WEB_SERVICE_ROOT_URL As String = Nothing

    Public Function GetListeCheck() As Stream Implements IWSPex.GetListeCheck
        Return ProtoStreamFromList(dicPexCM.Values.ToList, False)
    End Function

    Public Function GetListePexCommande() As Stream Implements IWSPex.GetListePexCommande
        Return ProtoStreamFromList(lstPexCmd, False)
    End Function

    Public Function GetListePexFacture() As Stream Implements IWSPex.GetListePexFacture
        Return ProtoStreamFromList(lstPexFac, False)
    End Function

    '-todo- params pour spécifier un seul mois? date de début?
    '-todo- prevent re-entrancy (ne pas partir le calcul si celui-ci est déja en cours...
    Public Function CalculCoutTransport() As Stream Implements IWSPex.CalculCoutTransport
        Dim res = CoutTransport.CalculCoutTransport()
        Return UtilitiesWeb.WriteBuffer(res, res.Length)
    End Function

    ' Met a jour la table STD_CATEGORIE_SURCHARGE_COUT_PV_ACTUEL
    Public Function CalculCoutPVActuel() As Stream Implements IWSPex.CalculCoutPVActuel
        Dim res = CoutPVActuel.CalculCoutPVActuel()
        Return UtilitiesWeb.WriteBuffer(res, res.Length)
    End Function

    ' Met a jour la table STD_PV_COUT_ACTUALISABLE (utilisée par la vue STD_PV_COUT)
    Public Function CalculSTDPVCoutActualisable(Optional id_pv As Integer = 0) As Stream Implements IWSPex.CalculSTDPVCoutActualisable
        If id_pv = 0 Then id_pv = -1 ' mettre "optional = -1" dans la définition de l'appel ne fonctionne pas; on remplace 0 par -1 pour l'interne
        Dim res = STDPVCoutActualisable.CalculSTDPVCoutActualisable(id_pv)
        Return UtilitiesWeb.WriteBuffer(res, res.Length)
    End Function

    ' Met a jour les densités et cout de la table structure
    Public Function CalculCoutStructure() As Stream Implements IWSPex.CalculCoutStructure
        Dim res = CoutStructure.CalculCoutStructure
        Return UtilitiesWeb.WriteBuffer(res, res.Length)
    End Function

    '* http://ti10:8184/WSPex/StartPolyExpertReport
    '* Le choix du port est dans /_TopShelf/AppStart.vb.
    Public Function StartPolyExpertReport(Optional ResponseEmail As String = "") As Stream Implements IWSPex.StartPolyExpertReport
        Dim BufOut As String = ""

        If _PolyExpertReportNetRunning = False Then
            SyncLock objLock
                If _PolyExpertReportNetRunning = False Then
                    Try
                        _PolyExpertReportNetRunning = True

                        InitCaches()
                        If DataOK() Then
                            BuildRnDResourcesSpending()
                            BuildCommandeMachine()
                            BuildCommande()
                            BuildFacture()
                            BuildRequisitionFacture()
                            CalculFinal()
                            If ResponseEmail <> "" Then SendEmail("", "PolyExpertReport terminé", "L'exécution de PolyExpertReport est terminée.",, ResponseEmail)
                        Else
                            Log($"Les données ne sont pas complètes, ou invalide.  Le processus est interrompu.", color:=ConsoleColor.Red)
                            If ResponseEmail <> "" Then SendEmail("", "PolyExpertReport interrompu", "Les données ne sont pas complètes, ou invalide.  Le processus est interrompu.",, ResponseEmail)
                        End If
                        ClearCaches()

                    Catch agr As AggregateException
                        Log($"### Une ou plusieurs erreurs se sont produites: {agr}")
                     Catch ex As Exception
                        Log($"### Une erreur est survenue: {ex}")
                        If ResponseEmail <> "" Then SendEmail("", "PolyExpertReport interrompu", "Une erreur est survenue : " + ex.Message,, ResponseEmail)
                    Finally
                        _PolyExpertReportNetRunning = False
                    End Try
                Else
                    Log($"### PolyExpertReportNet déjà en cours.  Impossible de démarrer une nouvelle instance.")
                    If ResponseEmail <> "" Then SendEmail("", "PolyExpertReport interrompu", "PolyExpertReportNet déjà en cours.  Impossible de démarrer une nouvelle instance.",, ResponseEmail)
                End If
            End SyncLock
        Else
            Log($"### PolyExpertReportNet déjà en cours.  Impossible de démarrer une nouvelle instance.")
            If ResponseEmail <> "" Then SendEmail("", "PolyExpertReport interrompu", "PolyExpertReportNet déjà en cours.  Impossible de démarrer une nouvelle instance.",, ResponseEmail)
        End If

        Return UtilitiesWeb.WriteBuffer(BufOut, BufOut.Length)
    End Function


    Public Function Version() As Stream Implements IWSPex.Version

        Console.WriteLine($"[{Threading.Thread.CurrentThread.ManagedThreadId:0000}] {WCF_NAME}.{GetCurrentMethodName()}")

        Dim ci = GetCallerInfo(OperationContext.Current)

        Dim BufOut As String = ""
        Dim asm As Assembly = Assembly.GetExecutingAssembly
        If Not asm Is Nothing Then
            Dim asmName As AssemblyName = asm.GetName
            Dim cns = GetDB(DB_ENV).GetConnectionString.RemoveToken("Password", ";"c)
            Dim sep = New String("="c, cns.Length + 20)
            BufOut = $"
{sep}
    Assembly name: {asmName.Name}
          Version: {asmName.Version}
      Compiled at: {File.GetLastWriteTime(asm.Location)}
Connection string: {cns}
   Webservice URL: {Web.WebOperationContext.Current.IncomingRequest.UriTemplateMatch.RequestUri.OriginalString}
     Current time: {Now}
       Memory use: {(GC.GetTotalMemory(False) / 1024 / 1024):#,0.##}M
      Called from: {ci.IPAddress}:{ci.Port} (HTTP {ci.Method})
{sep}
".Trim
        End If
        Return UtilitiesWeb.WriteBuffer(BufOut, BufOut.Length)

    End Function

End Class
