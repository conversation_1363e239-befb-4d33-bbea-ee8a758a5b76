﻿Public Class groupCM

    Public Property cmdMac As commande_machine
    Public Property id_rsn_scr As Integer
    Public Property structures As List(Of commande_structure)
    Public Property rouleaux As List(Of groupInfoRouleau)
    Public Property scrap As List(Of groupInfoScrap)

    Public Property totProd As totauxProduction

    Public Property pexCM As pexCommandeMachine

    'Public Property CMC As groupCMC

End Class

Public Class groupC

    Public Property cmd As commande
    Public Property idRsnScrap As Integer
    Public Property structures As List(Of commande_structure)
    Public Property rouleaux As List(Of groupInfoRouleau)
    Public Property scrap As List(Of groupInfoScrap)

    Public Property totProd As totauxProduction

    Public Property pexC As pexCommande

End Class