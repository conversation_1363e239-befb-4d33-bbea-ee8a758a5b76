﻿Imports ProtoBuf

<ProtoContract()>
<PetaPoco.PrimaryKey("ID_ADRESSE_CLIENT_HISTORIQUE")>
Partial Public Class adresse_client_historique
    <ProtoMember(1)> Public Property id_adresse_client_historique As Integer
    <ProtoMember(2)> Public Property id_adresse As Integer
    <ProtoMember(3)> Public Property id_client As Integer
    '<ProtoMember(4)> Public Property type As String
    <ProtoMember(5)> Public Property nom As String
    '<ProtoMember(6)> Public Property delais_transport As Integer?
    '<ProtoMember(7)> Public Property no_douane As String
    '<ProtoMember(8)> Public Property adresse As String
    '<ProtoMember(9)> Public Property adresse_2 As String
    '<ProtoMember(10)> Public Property ville As String
    '<ProtoMember(11)> Public Property province As String
    '<ProtoMember(12)> Public Property pays As String
    '<ProtoMember(13)> Public Property code_postal As String
    '<ProtoMember(14)> Public Property telephone_1 As String
    '<ProtoMember(15)> Public Property telephone_2 As String
    '<ProtoMember(16)> Public Property fax As String
    '<ProtoMember(17)> Public Property www As String
    '<ProtoMember(18)> Public Property email As String
    '<ProtoMember(19)> Public Property id_transport As Integer?
    '<ProtoMember(20)> Public Property id_taxe As Integer?
    '<ProtoMember(21)> Public Property faxer_commande As Boolean
    '<ProtoMember(22)> Public Property interurbain As Boolean
    '<ProtoMember(23)> Public Property commentaire As String
    '<ProtoMember(24)> Public Property com_transport As String
    '<ProtoMember(25)> Public Property destinataire As String
    '<ProtoMember(26)> Public Property faxexped As String
    '<ProtoMember(27)> Public Property destexped As String
    '<ProtoMember(28)> Public Property faxer_exped As Boolean
    '<ProtoMember(29)> Public Property formatintlfax As Boolean
    '<ProtoMember(30)> Public Property formatintltel1 As Boolean
    '<ProtoMember(31)> Public Property formatintltel2 As Boolean
    '<ProtoMember(32)> Public Property formatintlfaxconfexped As Boolean
    <ProtoMember(33)> Public Property id_secteur As Integer
    '<ProtoMember(34)> Public Property transport_route_std As Boolean
    '<ProtoMember(35)> Public Property envoi_courriel_exped As Boolean?
    '<ProtoMember(36)> Public Property courriel_exped As String
    '<ProtoMember(37)> Public Property id_secteur_old As Integer?
    '<ProtoMember(38)> Public Property id_adresse_entrepot_externe As Integer?
    <ProtoMember(39)> Public Property cout_entrepo_ext_us As Single
    <ProtoMember(40)> Public Property date_debut As DateTime
    <ProtoMember(41)> Public Property date_fin As DateTime
End Class