﻿Imports ProtoBuf

<PetaPoco.PrimaryKey("ID_STD_CATEGORIE_SURCHARGE")>
<ProtoContract>
Partial Public Class std_categorie_surcharge
    <ProtoMember(1)> Public Property id_std_categorie_surcharge As Integer
    <ProtoMember(2)> Public Property id_std_categorie As Integer
    <ProtoMember(3)> Public Property nom As Integer
    <ProtoMember(4)> Public Property date_debut As DateTime
    <ProtoMember(5)> Public Property date_fin As DateTime
    <ProtoMember(6)> Public Property quantite_min As Integer
    <ProtoMember(7)> Public Property quantite_max As Integer
    <ProtoMember(8)> Public Property surcharge As Decimal
    <ProtoMember(9)> Public Property pourcentage_scrap_1_couche As Decimal
    <ProtoMember(11)> Public Property pourcentage_scrap_3_couches As Decimal
    <ProtoMember(12)> Public Property pourcentage_scrap_7_couches As Decimal
    <ProtoMember(10)> Public Property commentaire As String
End Class