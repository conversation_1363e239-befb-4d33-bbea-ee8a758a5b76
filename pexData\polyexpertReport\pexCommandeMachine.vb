﻿Imports ProtoBuf

'<PetaPoco.PrimaryKey("UniqueID")>
<PetaPoco.TableName("CommandeMachine")>
<ProtoContract>
Partial Public Class pexCommandeMachine
    Implements ISortID
    <ProtoMember(1)> Public Property UniqueID As Long Implements ISortID.UniqueID
    <ProtoMember(2)> Public Property CommandeMachineID As Integer Implements ISortID.SortID
    <ProtoMember(3)> Public Property CommandeID As Integer Implements ISortID.SortID2
    <ProtoMember(4)> Public Property MachineID As Integer
    <ProtoMember(5)> Public Property Machine As String
    <ProtoMember(6)> Public Property DateDebutSetup As DateTime?
    <ProtoMember(7)> Public Property DateFinSetup As DateTime?
    <ProtoMember(8)> Public Property DateDebutCommande As DateTime?
    <ProtoMember(9)> Public Property DateFinCommande As DateTime?
    <ProtoMember(10)> Public Property BudgetSetupLb As Decimal
    <ProtoMember(11)> Public Property OrdreCedule As Integer
    <ProtoMember(12)> Public Property REELResineCanMontantLbs As Decimal
    <ProtoMember(13)> Public Property REELResineUsMontantLbs As Decimal
    <ProtoMember(14)> Public Property REELResineTauxUs As Decimal
    <ProtoMember(15)> Public Property REELResineScrapCanMontantLbs As Decimal
    <ProtoMember(16)> Public Property REELResineScrapUsMontantLbs As Decimal
    <ProtoMember(17)> Public Property REELResineScrapTauxUs As Decimal
    <ProtoMember(18)> Public Property REELProductionBrute As Decimal
    <ProtoMember(19)> Public Property REELProductionExpediable As Decimal
    <ProtoMember(20)> Public Property REELNbRouleauExpediable As Integer
    <ProtoMember(21)> Public Property REELRejetSetup As Decimal
    <ProtoMember(22)> Public Property REELRejetSetupCan As Decimal
    <ProtoMember(23)> Public Property REELRejetSetupUs As Decimal
    <ProtoMember(24)> Public Property REELRejetVrac As Decimal
    <ProtoMember(25)> Public Property REELRejetVracCan As Decimal
    <ProtoMember(26)> Public Property REELRejetVracUs As Decimal
    <ProtoMember(27)> Public Property REELRejetNCR As Decimal
    <ProtoMember(28)> Public Property REELRejetNCRCan As Decimal
    <ProtoMember(29)> Public Property REELRejetNCRUs As Decimal
    <ProtoMember(30)> Public Property REELRejetNCH As Decimal
    <ProtoMember(31)> Public Property REELRejetNCHCan As Decimal
    <ProtoMember(32)> Public Property REELRejetNCHUs As Decimal
    <ProtoMember(33)> Public Property REELRejetTotal As Decimal
    <ProtoMember(34)> Public Property REELRejetTotalCan As Decimal
    <ProtoMember(35)> Public Property REELRejetTotalUs As Decimal
    <ProtoMember(36)> Public Property REELRejetTotalReprocessCan As Decimal
    <ProtoMember(37)> Public Property REELRejetTotalExtrusionCan As Decimal
    <ProtoMember(38)> Public Property REELRejetTotalReprocessUs As Decimal
    <ProtoMember(39)> Public Property REELRejetTotalExtrusionUs As Decimal
    <ProtoMember(40)> Public Property REELRejetPourcentage As Decimal
    <ProtoMember(41)> Public Property REELScrapCanMontant As Decimal
    <ProtoMember(42)> Public Property REELScrapCanMontantLbs As Decimal
    <ProtoMember(43)> Public Property REELScrapUsMontant As Decimal
    <ProtoMember(44)> Public Property REELScrapUsMontantLbs As Decimal
    <ProtoMember(45)> Public Property Calculer As Boolean
    <ProtoMember(46)> Public Property REELResineSTDUsMontantLbs As Decimal
    <ProtoMember(47)> Public Property REELResineListeUsMontantLbs As Decimal
    <ProtoMember(48)> Public Property ReelMachineMontantCan As Decimal
    <ProtoMember(49)> Public Property REELMachineMontantUs As Decimal
    <ProtoMember(50)> Public Property REELModMontantCan As Decimal
    <ProtoMember(51)> Public Property REELModMontantUs As Decimal
    <ProtoMember(52)> Public Property REELMoiMontantCan As Decimal
    <ProtoMember(53)> Public Property REELMoiMontantUs As Decimal
    <ProtoMember(54)> Public Property REELCoutTotalResineCan As Decimal
    <ProtoMember(55)> Public Property REELCoutTotalResineUs As Decimal
    <ProtoMember(56)> Public Property RnDProject As String
    <ProtoMember(57)> Public Property TypeSampleID As Integer
    <ProtoMember(58)> Public Property TypeSampleCode As String
    <ProtoMember(59)> Public Property TypeSampleDescription As String
    <PetaPoco.Ignore> Public Property totProd As totauxProduction
End Class
