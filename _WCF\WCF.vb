﻿Imports System.ServiceModel
Imports System.ServiceModel.Description

Module WCF

    Private _wcfHost As ServiceHost = Nothing

    Public Sub WCFStart(ipPort As Integer)

        ' Add service endpoint
        Dim baseAddress As String = $"http://{Environment.MachineName}:{ipPort}/{WCF_NAME}"
        _wcfHost = New ServiceHost(GetType(WSPex), New Uri(baseAddress))
        _wcfHost.AddServiceEndpoint(GetType(IWSPex), New WebHttpBinding, "").Behaviors.Add(New WebHttpBehavior())

        ' Throttling
        Dim throttle As ServiceThrottlingBehavior
        throttle = _wcfHost.Description.Behaviors.Find(Of ServiceThrottlingBehavior)()
        If throttle Is Nothing Then
            throttle = New ServiceThrottlingBehavior()
            throttle.MaxConcurrentCalls = 30
            throttle.MaxConcurrentSessions = 48
            throttle.MaxConcurrentInstances = Integer.MaxValue    ' defaults are 10, 16 and Int32.MaxValue
            _wcfHost.Description.Behaviors.Add(throttle)
        End If

        ' Better error messages
        Dim dbg As ServiceDebugBehavior
        dbg = _wcfHost.Description.Behaviors.Find(Of ServiceDebugBehavior)()
        If dbg Is Nothing Then
            dbg = New ServiceDebugBehavior
            dbg.IncludeExceptionDetailInFaults = True
            _wcfHost.Description.Behaviors.Add(dbg)
        End If

        _wcfHost.Open()
        Log($"### WCF Server started at {baseAddress}")

    End Sub

    Public Sub WCFStop()
        If _wcfHost IsNot Nothing Then
            _wcfHost.Close()
            _wcfHost = Nothing
        End If
    End Sub

End Module
