﻿Imports ProtoBuf

<PetaPoco.PrimaryKey("ID_PLAINTE_ENTENTE")>
<ProtoContract()>
Partial Public Class plainte_entente
    <ProtoMember(1)> Public Property id_plainte_entente As Integer
    <ProtoMember(2)> Public Property id_plainte As Integer
    'Public Property date_entree As DateTime?
    'Public Property id_plainte_approbation_entente As Integer?
    'Public Property id_employe As Integer?
    'Public Property id_vendeur As Integer?
    'Public Property description As String
    'Public Property date_entente As DateTime?
    'Public Property id_devise As Integer?
    'Public Property id_etat_entente As Integer?
End Class