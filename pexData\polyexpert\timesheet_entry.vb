﻿Imports ProtoBuf

<ProtoContract>
<PetaPoco.TableName("TIMESHEET_ENTRY")>
<PetaPoco.PrimaryKey("id_timesheet_entry")>
Partial Public Class timesheet_entry

    <ProtoMember(1)>
    Public Property id_timesheet_entry As Integer

    <ProtoMember(2)>
    Public Property id_employe As Integer

    ' DATE can be nullable in DB; keep it nullable here to be safe
    <ProtoMember(3)>
    Public Property [date] As DateTime?

    ' Hours worked; decimal to preserve fractions (e.g., 7.5)
    <ProtoMember(4)>
    Public Property heures As Decimal

    <ProtoMember(5)>
    Public Property id_timesheet_task As Integer

    <ProtoMember(6)>
    Public Property id_timesheet_task_type As Integer

    <PetaPoco.ResultColumn>
    <ProtoMember(7)>
    Public Property task_nom As String          ' from TIMESHEET_TASK.NOM

    <PetaPoco.ResultColumn>
    <ProtoMember(8)>
    Public Property task_type_nom As String     ' from TIMESHEET_TASK_TYPE.NOM

    ' NEW: description of the task (projected from a JOIN, not stored in TIMESHEET_ENTRY)
    <PetaPoco.ResultColumn>
    <ProtoMember(9)>
    Public Property timesheet_task_description As String   ' e.g., from TIMESHEET_TASK.DESCRIPTION

    <PetaPoco.Ignore, ProtoMember(10)>
    Public Property heures_ajustées As Decimal?

    <PetaPoco.Ignore, ProtoMember(11)>
    Public Property taux_horaire As Decimal?

End Class
