{"RootPath": "C:\\Dev\\WSPex\\WSPex", "ProjectFileName": "WSPex.vbproj", "Configuration": "Debug|AnyCPU", "FrameworkPath": "", "Sources": [{"SourceFile": "Common.vb"}, {"SourceFile": "CoutPVActuel\\CoutPVActuel.vb"}, {"SourceFile": "CoutPVActuel\\STDPVCoutActualisable.vb"}, {"SourceFile": "CoutStructure\\CoutStructure.vb"}, {"SourceFile": "CoutTransport\\CoutTransport.vb"}, {"SourceFile": "CoutTransport\\DBUpdater.vb"}, {"SourceFile": "CoutTransport\\LogFile.vb"}, {"SourceFile": "Logger.vb"}, {"SourceFile": "My Project\\AssemblyInfo.vb"}, {"SourceFile": "My Project\\Application.Designer.vb"}, {"SourceFile": "My Project\\Resources.Designer.vb"}, {"SourceFile": "My Project\\Settings.Designer.vb"}, {"SourceFile": "NetMQ\\NetMQ.vb"}, {"SourceFile": "pexData\\pexReportDRequisitionFacture.vb"}, {"SourceFile": "pexData\\pexReportECalculFinal.vb"}, {"SourceFile": "pexData\\pexReport_Data.vb"}, {"SourceFile": "pexData\\pexReportACommandeMachine.vb"}, {"SourceFile": "pexData\\pexReportBCommande.vb"}, {"SourceFile": "pexData\\pexReportCFacture.vb"}, {"SourceFile": "pexData\\polyexpertReport\\groupCM.vb"}, {"SourceFile": "pexData\\polyexpertReport\\groupInfo.vb"}, {"SourceFile": "pexData\\polyexpertReport\\groupUtil.vb"}, {"SourceFile": "pexData\\polyexpertReport\\pexCommande.vb"}, {"SourceFile": "pexData\\polyexpertReport\\pexCommandeMachine.vb"}, {"SourceFile": "pexData\\polyexpertReport\\pexFacture.vb"}, {"SourceFile": "pexData\\polyexpertReport\\pexFactureMensuel.vb"}, {"SourceFile": "pexData\\polyexpertReport\\pexRequisitionFacture.vb"}, {"SourceFile": "pexData\\polyexpertReport\\totauxProduction.vb"}, {"SourceFile": "pexData\\polyexpert\\adresse_client_historique.vb"}, {"SourceFile": "pexData\\polyexpert\\classe.vb"}, {"SourceFile": "pexData\\polyexpert\\client.vb"}, {"SourceFile": "pexData\\polyexpert\\client_historique.vb"}, {"SourceFile": "pexData\\polyexpert\\commande.vb"}, {"SourceFile": "pexData\\polyexpert\\commande_machine.vb"}, {"SourceFile": "pexData\\polyexpert\\commande_machine_scrap.vb"}, {"SourceFile": "pexData\\polyexpert\\commande_machine_scrap_pourc.vb"}, {"SourceFile": "pexData\\polyexpert\\commande_palette.vb"}, {"SourceFile": "pexData\\polyexpert\\commande_requisition_transport.vb"}, {"SourceFile": "pexData\\polyexpert\\commande_structure.vb"}, {"SourceFile": "pexData\\polyexpert\\devise.vb"}, {"SourceFile": "pexData\\polyexpert\\expedition.vb"}, {"SourceFile": "pexData\\polyexpert\\facture.vb"}, {"SourceFile": "pexData\\polyexpert\\facture_item.vb"}, {"SourceFile": "pexData\\polyexpert\\forme.vb"}, {"SourceFile": "pexData\\polyexpert\\listeSecteur.vb"}, {"SourceFile": "pexData\\polyexpert\\ListeSecteurHistorique.vb"}, {"SourceFile": "pexData\\polyexpert\\machine.vb"}, {"SourceFile": "pexData\\polyexpert\\marche.vb"}, {"SourceFile": "pexData\\polyexpert\\plainte.vb"}, {"SourceFile": "pexData\\polyexpert\\plainte_entente.vb"}, {"SourceFile": "pexData\\polyexpert\\produit.vb"}, {"SourceFile": "pexData\\polyexpert\\produit_categ.vb"}, {"SourceFile": "pexData\\polyexpert\\produit_client.vb"}, {"SourceFile": "pexData\\polyexpert\\pv.vb"}, {"SourceFile": "pexData\\polyexpert\\requisition_transport.vb"}, {"SourceFile": "pexData\\polyexpert\\resine_cout_historique.vb"}, {"SourceFile": "pexData\\polyexpert\\resine_type_historique.vb"}, {"SourceFile": "pexData\\polyexpert\\rouleau.vb"}, {"SourceFile": "pexData\\polyexpert\\statut.vb"}, {"SourceFile": "pexData\\polyexpert\\statut_expedition.vb"}, {"SourceFile": "pexData\\polyexpert\\statut_facturation.vb"}, {"SourceFile": "pexData\\polyexpert\\std_categorie_surcharge.vb"}, {"SourceFile": "pexData\\polyexpert\\std_cout_transport_adresse.vb"}, {"SourceFile": "pexData\\polyexpert\\std_produit.vb"}, {"SourceFile": "pexData\\polyexpert\\std_produit_client.vb"}, {"SourceFile": "pexData\\polyexpert\\std_pv_cout.vb"}, {"SourceFile": "pexData\\polyexpert\\std_pv_resine_historique.vb"}, {"SourceFile": "pexData\\polyexpert\\std_resine_historique.vb"}, {"SourceFile": "pexData\\polyexpert\\std_taux.vb"}, {"SourceFile": "pexData\\polyexpert\\std_variable_globale.vb"}, {"SourceFile": "pexData\\polyexpert\\taux_change.vb"}, {"SourceFile": "pexData\\polyexpert\\territoire.vb"}, {"SourceFile": "pexData\\polyexpert\\transport.vb"}, {"SourceFile": "pexData\\polyexpert\\type_rejet_dispo.vb"}, {"SourceFile": "pexData\\polyexpert\\vendeur.vb"}, {"SourceFile": "pexData\\polyexpert\\vendeur_historique.vb"}, {"SourceFile": "utilsNetMQ.vb"}, {"SourceFile": "_TopShelf\\AppStart.vb"}, {"SourceFile": "_TopShelf\\SelfHost.vb"}, {"SourceFile": "_WCF\\IWSPex.vb"}, {"SourceFile": "_WCF\\WCF.vb"}, {"SourceFile": "_WCF\\WSPex.vb"}, {"SourceFile": "obj\\Debug\\.NETFramework,Version=v4.7.2.AssemblyAttributes.vb"}], "References": [{"Reference": "C:\\Dev\\WSPex\\packages\\AsyncIO.0.1.69\\lib\\net40\\AsyncIO.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\Microsoft.CSharp.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Dev\\WSPex\\packages\\NetMQ.*******\\lib\\net40\\NetMQ.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Dev\\_Shared\\PetaPoco.Net40\\PetaPoco\\bin\\Debug\\PetaPoco.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "C:\\Dev\\_Shared\\PetaPoco.Net40\\PetaPoco\\bin\\Debug\\PetaPoco.dll"}, {"Reference": "C:\\Dev\\WSPex\\packages\\protobuf-net.2.4.0\\lib\\net40\\protobuf-net.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Configuration.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Configuration.Install.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Data.DataSetExtensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Deployment.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Net.Http.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Runtime.Caching.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Runtime.Serialization.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.ServiceModel.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.ServiceModel.Web.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.ServiceProcess.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Xml.Linq.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Dev\\WSPex\\packages\\Topshelf.4.2.0\\lib\\net452\\Topshelf.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Dev\\_Shared\\Utilities\\bin\\Debug\\Utilities.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "C:\\Dev\\_Shared\\Utilities\\bin\\Debug\\Utilities.dll"}, {"Reference": "C:\\Dev\\_Shared\\UtilitiesData\\bin\\Debug\\UtilitiesData.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "C:\\Dev\\_Shared\\UtilitiesData\\bin\\Debug\\UtilitiesData.dll"}, {"Reference": "C:\\Dev\\_Shared\\UtilitiesWeb\\bin\\Debug\\UtilitiesWeb.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "C:\\Dev\\_Shared\\UtilitiesWeb\\bin\\Debug\\UtilitiesWeb.dll"}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "C:\\Dev\\WSPex\\WSPex\\bin\\Debug\\WSPex.exe", "OutputItemRelativePath": "WSPex.exe"}, {"OutputItemFullPath": "", "OutputItemRelativePath": ""}], "CopyToOutputEntries": []}