﻿Imports ProtoBuf

<PetaPoco.TableName("dbo.AUDIT_MACHINE")>
<PetaPoco.PrimaryKey("AUDIT_ID", autoIncrement:=True)>
<ProtoContract()>
Partial Public Class audit_machine
    <ProtoMember(1)> Public Property AUDIT_ID As Integer
    <ProtoMember(2)> Public Property id_machine As Integer
    <ProtoMember(3)> Public Property audit_date_deb As Date?
    <ProtoMember(4)> Public Property audit_date_fin As Date?
    <ProtoMember(5)> Public Property reel_machine_can_montant_heure As Decimal?
End Class
