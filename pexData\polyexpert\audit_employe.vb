﻿Imports ProtoBuf

<ProtoContract>
<PetaPoco.TableName("AUDIT_EMPLOYE")>
<PetaPoco.PrimaryKey("AUDIT_ID")>
Partial Public Class audit_employe
    <ProtoMember(1)> Public Property audit_id As Integer
    <ProtoMember(2)> Public Property id_employe As Integer
    <ProtoMember(3)> Public Property STD_COUT_CAN_HEURE As Decimal?
    <ProtoMember(4)> Public Property nb_heures_hebdo_payees As Integer?
    <ProtoMember(5)> Public Property audit_date_deb As DateTime?
    <ProtoMember(6)> Public Property audit_date_fin As DateTime?
    <ProtoMember(7)> Public Property audit_user As String
    <ProtoMember(8)> Public Property audit_host As String
    <ProtoMember(9)> Public Property audit_prog As String
    <ProtoMember(10)> Public Property audit_oper As String
    <ProtoMember(11)> Public Property audit_raison As String
End Class
