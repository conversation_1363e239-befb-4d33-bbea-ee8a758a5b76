﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{C3DA47D4-F242-46AC-A0B1-AAA5D6C9DA64}</ProjectGuid>
    <OutputType>Exe</OutputType>
    <StartupObject>WSPex.AppStart</StartupObject>
    <RootNamespace>WSPex</RootNamespace>
    <AssemblyName>WSPex</AssemblyName>
    <FileAlignment>512</FileAlignment>
    <MyType>Console</MyType>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <TargetFrameworkProfile />
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>x64</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <OutputPath>bin\Debug\</OutputPath>
    <DocumentationFile>WSPex.xml</DocumentationFile>
    <NoWarn>
    </NoWarn>
    <WarningsAsErrors>41999,42016,42017,42018,42019,42020,42021,42022,42032,42036</WarningsAsErrors>
    <Prefer32Bit>false</Prefer32Bit>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <CodeAnalysisRuleSet>
    </CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>x64</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <Optimize>true</Optimize>
    <OutputPath>bin\x64\Release\</OutputPath>
    <DocumentationFile>WSPex.xml</DocumentationFile>
    <NoWarn>
    </NoWarn>
    <WarningsAsErrors>41999,42016,42017,42018,42019,42020,42021,42022,42032,42036</WarningsAsErrors>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup>
    <OptionExplicit>On</OptionExplicit>
  </PropertyGroup>
  <PropertyGroup>
    <OptionCompare>Binary</OptionCompare>
  </PropertyGroup>
  <PropertyGroup>
    <OptionStrict>On</OptionStrict>
  </PropertyGroup>
  <PropertyGroup>
    <OptionInfer>On</OptionInfer>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugSymbols>true</DebugSymbols>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <OutputPath>bin\x64\Debug\</OutputPath>
    <DocumentationFile>WSPex.xml</DocumentationFile>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <WarningsAsErrors>41999,42016,42017,42018,42019,42020,42021,42022,42032,42036</WarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <DefineTrace>true</DefineTrace>
    <OutputPath>bin\x64\Release\</OutputPath>
    <DocumentationFile>WSPex.xml</DocumentationFile>
    <Optimize>true</Optimize>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <WarningsAsErrors>41999,42016,42017,42018,42019,42020,42021,42022,42032,42036</WarningsAsErrors>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AsyncIO, Version=0.1.69.0, Culture=neutral, PublicKeyToken=44a94435bd6f33f8, processorArchitecture=MSIL">
      <HintPath>..\packages\AsyncIO.0.1.69\lib\net40\AsyncIO.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="NetMQ, Version=4.0.0.1, Culture=neutral, PublicKeyToken=a6decef4ddc58b3a, processorArchitecture=MSIL">
      <HintPath>..\packages\NetMQ.4.0.0.1\lib\net40\NetMQ.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="protobuf-net, Version=2.4.0.0, Culture=neutral, PublicKeyToken=257b51d87d2e4d67, processorArchitecture=MSIL">
      <HintPath>..\packages\protobuf-net.2.4.0\lib\net40\protobuf-net.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Configuration.Install" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Runtime.Caching" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.ServiceModel.Web" />
    <Reference Include="System.ServiceProcess" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Net.Http" />
    <Reference Include="Topshelf, Version=4.2.0.194, Culture=neutral, PublicKeyToken=b800c4cfcdeea87b, processorArchitecture=MSIL">
      <HintPath>..\packages\Topshelf.4.2.0\lib\net452\Topshelf.dll</HintPath>
      <Private>True</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Import Include="Microsoft.VisualBasic" />
    <Import Include="System" />
    <Import Include="System.Collections" />
    <Import Include="System.Collections.Generic" />
    <Import Include="System.Data" />
    <Import Include="System.Diagnostics" />
    <Import Include="System.Linq" />
    <Import Include="System.Xml.Linq" />
    <Import Include="System.Threading.Tasks" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Common.vb" />
    <Compile Include="CoutPVActuel\CoutPVActuel.vb" />
    <Compile Include="CoutPVActuel\STDPVCoutActualisable.vb" />
    <Compile Include="CoutStructure\CoutStructure.vb" />
    <Compile Include="CoutTransport\CoutTransport.vb" />
    <Compile Include="CoutTransport\DBUpdater.vb" />
    <Compile Include="CoutTransport\LogFile.vb" />
    <Compile Include="Logger.vb" />
    <Compile Include="My Project\AssemblyInfo.vb" />
    <Compile Include="My Project\Application.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Application.myapp</DependentUpon>
    </Compile>
    <Compile Include="My Project\Resources.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="My Project\Settings.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <Compile Include="NetMQ\NetMQ.vb" />
    <Compile Include="pexData\pexReportDRequisitionFacture.vb" />
    <Compile Include="pexData\pexReportECalculFinal.vb" />
    <Compile Include="pexData\pexReportRnDResourcesSpending.vb" />
    <Compile Include="pexData\pexReport_Data.vb" />
    <Compile Include="pexData\pexReportACommandeMachine.vb" />
    <Compile Include="pexData\pexReportBCommande.vb" />
    <Compile Include="pexData\pexReportCFacture.vb" />
    <Compile Include="pexData\polyexpertReport\groupCM.vb" />
    <Compile Include="pexData\polyexpertReport\groupInfo.vb" />
    <Compile Include="pexData\polyexpertReport\groupUtil.vb" />
    <Compile Include="pexData\polyexpertReport\pexCommande.vb" />
    <Compile Include="pexData\polyexpertReport\pexCommandeMachine.vb" />
    <Compile Include="pexData\polyexpertReport\pexFacture.vb" />
    <Compile Include="pexData\polyexpertReport\pexFactureMensuel.vb" />
    <Compile Include="pexData\polyexpertReport\pexRequisitionFacture.vb" />
    <Compile Include="pexData\polyexpertReport\pexRnDResourcesSpending.vb" />
    <Compile Include="pexData\polyexpertReport\totauxProduction.vb" />
    <Compile Include="pexData\polyexpert\adresse_client_historique.vb" />
    <Compile Include="pexData\polyexpert\audit_employe.vb" />
    <Compile Include="pexData\polyexpert\audit_machine.vb" />
    <Compile Include="pexData\polyexpert\classe.vb" />
    <Compile Include="pexData\polyexpert\client.vb" />
    <Compile Include="pexData\polyexpert\client_historique.vb" />
    <Compile Include="pexData\polyexpert\commande.vb" />
    <Compile Include="pexData\polyexpert\commande_machine.vb" />
    <Compile Include="pexData\polyexpert\commande_machine_scrap.vb" />
    <Compile Include="pexData\polyexpert\commande_machine_scrap_pourc.vb" />
    <Compile Include="pexData\polyexpert\commande_palette.vb" />
    <Compile Include="pexData\polyexpert\commande_requisition_transport.vb" />
    <Compile Include="pexData\polyexpert\commande_structure.vb" />
    <Compile Include="pexData\polyexpert\devise.vb" />
    <Compile Include="pexData\polyexpert\employe.vb" />
    <Compile Include="pexData\polyexpert\expedition.vb" />
    <Compile Include="pexData\polyexpert\facture.vb" />
    <Compile Include="pexData\polyexpert\facture_item.vb" />
    <Compile Include="pexData\polyexpert\forme.vb" />
    <Compile Include="pexData\polyexpert\listeSecteur.vb" />
    <Compile Include="pexData\polyexpert\ListeSecteurHistorique.vb" />
    <Compile Include="pexData\polyexpert\machine.vb" />
    <Compile Include="pexData\polyexpert\marche.vb" />
    <Compile Include="pexData\polyexpert\plainte.vb" />
    <Compile Include="pexData\polyexpert\plainte_entente.vb" />
    <Compile Include="pexData\polyexpert\produit.vb" />
    <Compile Include="pexData\polyexpert\produit_categ.vb" />
    <Compile Include="pexData\polyexpert\produit_client.vb" />
    <Compile Include="pexData\polyexpert\pv.vb" />
    <Compile Include="pexData\polyexpert\requisition_transport.vb" />
    <Compile Include="pexData\polyexpert\resine_cout_historique.vb" />
    <Compile Include="pexData\polyexpert\resine_type_historique.vb" />
    <Compile Include="pexData\polyexpert\rouleau.vb" />
    <Compile Include="pexData\polyexpert\statut.vb" />
    <Compile Include="pexData\polyexpert\statut_expedition.vb" />
    <Compile Include="pexData\polyexpert\statut_facturation.vb" />
    <Compile Include="pexData\polyexpert\std_categorie_surcharge.vb" />
    <Compile Include="pexData\polyexpert\std_cout_transport_adresse.vb" />
    <Compile Include="pexData\polyexpert\std_produit.vb" />
    <Compile Include="pexData\polyexpert\std_produit_client.vb" />
    <Compile Include="pexData\polyexpert\std_pv_cout.vb" />
    <Compile Include="pexData\polyexpert\std_pv_resine_historique.vb" />
    <Compile Include="pexData\polyexpert\std_resine_historique.vb" />
    <Compile Include="pexData\polyexpert\std_taux.vb" />
    <Compile Include="pexData\polyexpert\std_variable_globale.vb" />
    <Compile Include="pexData\polyexpert\taux_change.vb" />
    <Compile Include="pexData\polyexpert\territoire.vb" />
    <Compile Include="pexData\polyexpert\timesheet_entry.vb" />
    <Compile Include="pexData\polyexpert\transport.vb" />
    <Compile Include="pexData\polyexpert\type_rejet_dispo.vb" />
    <Compile Include="pexData\polyexpert\type_sample.vb" />
    <Compile Include="pexData\polyexpert\vendeur.vb" />
    <Compile Include="pexData\polyexpert\vendeur_historique.vb" />
    <Compile Include="utilsNetMQ.vb" />
    <Compile Include="_TopShelf\AppStart.vb" />
    <Compile Include="_TopShelf\SelfHost.vb" />
    <Compile Include="_WCF\IWSPex.vb" />
    <Compile Include="_WCF\WCF.vb" />
    <Compile Include="_WCF\WSPex.vb" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="My Project\Resources.resx">
      <Generator>VbMyResourcesResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.vb</LastGenOutput>
      <CustomToolNamespace>My.Resources</CustomToolNamespace>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="..\.editorconfig">
      <Link>.editorconfig</Link>
    </None>
    <None Include="bin\x64\Debug\WSPex.ini" />
    <None Include="My Project\Application.myapp">
      <Generator>MyApplicationCodeGenerator</Generator>
      <LastGenOutput>Application.Designer.vb</LastGenOutput>
    </None>
    <None Include="My Project\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <CustomToolNamespace>My</CustomToolNamespace>
      <LastGenOutput>Settings.Designer.vb</LastGenOutput>
    </None>
    <None Include="App.config" />
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\_Shared\PetaPoco.Net40\PetaPoco\PetaPoco_SIGNED.csproj">
      <Project>{baa2d866-862b-413a-8b2b-ebddadab6c2e}</Project>
      <Name>PetaPoco_SIGNED</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\_Shared\UtilitiesData\UtilitiesData.vbproj">
      <Project>{b9a51568-d04a-4086-9f3a-05288dcd5832}</Project>
      <Name>UtilitiesData</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\_Shared\UtilitiesWeb\UtilitiesWeb.vbproj">
      <Project>{181af15d-d792-4c74-acff-9a7e0e94047f}</Project>
      <Name>UtilitiesWeb</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\_Shared\Utilities\Utilities.vbproj">
      <Project>{4bdf5339-bfea-469e-b65c-cfa5464e14a5}</Project>
      <Name>Utilities</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include=".NETFramework,Version=v4.7.2">
      <Visible>False</Visible>
      <ProductName>Microsoft .NET Framework 4.7.2 %28x86 and x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <Content Include="README_VALIDATION.TXT" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.VisualBasic.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>