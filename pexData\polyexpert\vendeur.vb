﻿Imports ProtoBuf

<PetaPoco.PrimaryKey("ID_VENDEUR")>
<ProtoContract>
Partial Public Class vendeur
    <ProtoMember(1)> Public Property id_vendeur As Integer
    <ProtoMember(2)> Public Property nom As String
    '	Public Property telephone As String
    '	Public Property fax As String
    '	Public Property email As String
    '	Public Property interurbain As Boolean
    '	Public Property taux_commission As Decimal?
    '	Public Property formatintltel As Boolean
    '	Public Property formatintlfax As Boolean
    '	Public Property mets_vpmm As Boolean
    '	Public Property nom_complet As String
    '	Public Property actif As Boolean
    '	Public Property byemail As Boolean?
    '	Public Property email_pdf As String
End Class