﻿Module groupUtil

    Public Function GetGroupCM(cm As commande_machine) As groupCM
        Dim gcm = New groupCM With {
            .cmdMac = cm,
            .id_rsn_scr = GetResineScrap(cm.id_commande_machine, cm.id_commande, If(cm.date_debut_setup, Now)), '-todo- Modifier pour aller voir la vrai résine recyclée de chacun des cmd_mac_scrap.
            .rouleaux = New List(Of groupInfoRouleau),
            .scrap = New List(Of groupInfoScrap),
            .structures = GetStructs(cm.id_commande_machine) ', cm.id_commande)
            }
        'gcm.CMC = GetCMC(cm.id_commande_machine, cm.id_commande)

        Return gcm

    End Function

    Public Function GetGroupInfoRouleau(rou As rouleau, gcm As groupCM, lstSVG As List(Of std_variable_globale)) As groupInfoRouleau

        Dim gir = New groupInfoRouleau

        gir.cmdStr = GetStructureForDate(gcm.structures, rou.date_rouleau)
        gir.rou = rou
        If rou.id_conformite >= 2 Then
            If rou.date_rouleau < #2021-09-01# Or rou.id_conformite = 2 Then
                '* Garder le calcul de scrap d'avant pour les vieux rouleaux, et aussi pour les rouleaux en attente vu que c'est pas encore de la scrap.
                gir.coutsRsnScrap = GetResineCoutHistorique(gcm.id_rsn_scr, rou.date_rouleau)
            Else
                Dim tmp = scrap_rouleau.Where(Function(x) x.id_rouleau = rou.id_rouleau) '-todo- -slow-

                If tmp.Count > 1 Then
                    ' enlever les +/- (correction)
                    tmp = tmp.GroupBy(Function(x) x.id_type_rejet_dispo).Where(Function(g) g.Sum(Function(x) x.poids_scrap) <> 0).SelectMany(Function(g) g).OrderByDescending(Function(x) x.poids_scrap)
                End If
#If DEBUG Then
                If tmp.Select(Function(x) x.id_type_rejet_dispo).Distinct.Count > 1 Then
                    Debug.Write("")
                End If
#End If
                Dim scrap = tmp.FirstOrDefault()
                ' Notes: 2022-03-08: erreur causée par scrap = nothing         ( ### Une erreur est survenue : Object reference not set to an instance of an object.)
                '        2022-03-14: erreur causée par id_type_rejet_dispo = 0 ( ### Une erreur est survenue : The given key was not present in the dictionary.)
                If scrap IsNot Nothing AndAlso scrap.id_type_rejet_dispo > 0 Then
                    Dim ResineID = dicTypeRejetDispo(scrap.id_type_rejet_dispo).id_resine
                    gir.coutsRsnScrap = GetResineCoutHistorique(ResineID, rou.date_rouleau)
                Else
                    Debug.Write("")
                    gir.coutsRsnScrap = New resine_cout_historique ' default? -gg-
                End If
            End If

            gir.tauxChange = GetTauxChange(rou.date_rouleau)
            gir.varGlb = lstSVG.Where(Function(x) rou.date_rouleau >= x.date_debut AndAlso rou.date_rouleau < x.date_fin).Single
        End If

        Return gir

    End Function

    Public Function GetGroupInfoScrap(cms As commande_machine_scrap, gcm As groupCM, lstSVG As List(Of std_variable_globale)) As groupInfoScrap

        Dim gis = New groupInfoScrap

        gis.cmd_str = GetStructureForDate(gcm.structures, cms.date_scrap)
        gis.cmd_mac_scr = cms

        If cms.date_scrap < #2021-09-01# Then
            gis.rsn_scr_cou_his = GetResineCoutHistorique(gcm.id_rsn_scr, cms.date_scrap)
        Else
            Dim ResineID = dicTypeRejetDispo(cms.id_type_rejet_dispo).id_resine
            gis.rsn_scr_cou_his = GetResineCoutHistorique(ResineID, cms.date_scrap)
        End If
        gis.tau_chg = GetTauxChange(cms.date_scrap)
        gis.tau_chg_rsn = GetTauxChangeResineScrap(cms.date_scrap)
        gis.std_var_glb = lstSVG.Where(Function(x) cms.date_scrap >= x.date_debut AndAlso cms.date_scrap < x.date_fin).Single

        Return gis

    End Function

    Public Function GetGroupCmdInfoScrap(cms As commande_machine_scrap, gc As groupC, lstSVG As List(Of std_variable_globale)) As groupInfoScrap
        Dim gis = New groupInfoScrap With {
            .cmd_mac_scr = cms,
            .rsn_scr_cou_his = GetResineCoutHistorique(gc.idRsnScrap, cms.date_scrap),
            .tau_chg = GetTauxChange(cms.date_scrap),
            .std_var_glb = lstSVG.Where(Function(x) cms.date_scrap >= x.date_debut AndAlso cms.date_scrap < x.date_fin).Single
        }

        Return gis

    End Function

End Module
