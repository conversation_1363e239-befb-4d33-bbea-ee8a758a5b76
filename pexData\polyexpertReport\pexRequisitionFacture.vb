﻿Imports ProtoBuf

'<PetaPoco.PrimaryKey("RequisitionFactureId")>
<ProtoContract()>
<PetaPoco.TableName("RequisitionFacture")>
Partial Public Class pexRequisitionFacture
    Implements ISortID
    <ProtoMember(1)> Public Property UniqueID As Long Implements ISortID.UniqueID
    <ProtoMember(2)> Public Property RequisitionID As Integer Implements ISortID.SortID
    <ProtoMember(3)> Public Property CommandeID As Integer
    <ProtoMember(4)> Public Property FactureID As Integer Implements ISortID.SortID2
    <ProtoMember(5)> Public Property DeviseID As Integer
    <ProtoMember(6)> Public Property RequisitionDate As DateTime
    <ProtoMember(7)> Public Property RequisitionTauxUs As Decimal
    <ProtoMember(8)> Public Property RequisitionCanMontant As Decimal
    <ProtoMember(9)> Public Property RequisitionUsMontant As Decimal
    <ProtoMember(10)> Public Property RequisitionCanMontantLbs As Decimal?
    <ProtoMember(11)> Public Property RequisitionUsMontantLbs As Decimal
    <ProtoMember(12)> Public Property RequisitionFactureLbs As Decimal
    <ProtoMember(13)> Public Property RequisitionFactureCanMontant As Decimal
    <ProtoMember(14)> Public Property RequisitionFactureUsMontant As Decimal
    <ProtoMember(15)> Public Property Calculer As Boolean
    <ProtoMember(16)> Public Property REELTransportCoutTransportCanMontantLbs As Decimal
    <ProtoMember(17)> Public Property REELTransportCoutTransportUsMontantLbs As Decimal
    <ProtoMember(18)> Public Property REELTransportDouaneCanMontantLbs As Decimal
    <ProtoMember(19)> Public Property REELTransportDouaneUsMontantLbs As Decimal
    <ProtoMember(20)> Public Property REELTransportEntreposageCanMontantLbs As Decimal
    <ProtoMember(21)> Public Property REELTransportEntreposageUsMontantLbs As Decimal
    <ProtoMember(22)> Public Property REELTransportRabaisClientCanMontantLbs As Decimal
    <ProtoMember(23)> Public Property REELTransportRabaisClientUsMontantLbs As Decimal
    <ProtoMember(24)> Public Property REELTransportEntrepotCanMontantLbs As Decimal
    <ProtoMember(25)> Public Property REELTransportEntrepotUsMontantLbs As Decimal
End Class
