﻿Imports Topshelf

Module AppStart

    Public Const WCF_NAME = "WSPex"
    'Public Const WCF_PORT = 8184
    Public Const NMQ_PORT = 8185
    Public _PolyExpertReportNetRunning As Boolean = False

    ' from: http://blog.amosti.net/self-hosted-http-service-in-c-with-nancy-and-topshelf/

    ' Service:
    '<WCF_NAME>.exe install
    '<WCF_NAME>.exe start

    '<WCF_NAME>.exe stop
    '<WCF_NAME>.exe uninstall

    Sub Main()
        HostFactory.Run(Sub(configurator)

                            'x.UseLinuxIfAvailable() ' nuget: Install-Package Topshelf.Linux
                            configurator.Service(Of SelfHost)(Sub(service)
                                                                  service.ConstructUsing(Function() New SelfHost())
                                                                  service.WhenStarted(Function(notifier, hostControl) notifier.Start(hostControl))
                                                                  service.WhenStopped(Function(notifier, hostControl) notifier.Stop(hostControl))
                                                              End Sub)

                            configurator.SetDescription(WCF_NAME)
                            configurator.SetDisplayName(WCF_NAME)
                            configurator.SetServiceName(WCF_NAME)

                            'configurator.RunAsLocalSystem()
#If DEBUG Then
                            configurator.RunAs("PolyExpert\PEService", "$$PE$Service$$")
#Else
                            'configurator.RunAsPrompt ' avoid putting user/pass in source
                            configurator.RunAs("PolyExpert\PEService", "$$PE$Service$$")
#End If
                            Const OneMinute = 1 ' self-documenting...
                            configurator.EnableServiceRecovery(Sub(recovery)
                                                                   recovery.RestartService(OneMinute)
                                                                   recovery.RestartService(OneMinute)
                                                                   recovery.RestartService(OneMinute)
                                                               End Sub)
                        End Sub)
    End Sub

End Module
