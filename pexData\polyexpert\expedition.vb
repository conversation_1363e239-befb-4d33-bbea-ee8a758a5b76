﻿Imports ProtoBuf

<ProtoContract()>
<PetaPoco.PrimaryKey("ID_EXPEDITION")>
Partial Public Class expedition
    <ProtoMember(1)> Public Property id_expedition As Integer
    '<ProtoMember(2)> Public Property id_commande As Integer
    <ProtoMember(3)> Public Property date_exped As DateTime
    <ProtoMember(4)> Public Property id_client As Integer
    <ProtoMember(5)> Public Property id_adresse_client As Integer
    <ProtoMember(6)> Public Property id_transport As Integer
    '<ProtoMember(7)> Public Property date_impression As DateTime
    '<ProtoMember(8)> Public Property id_voiturier As Integer
    '<ProtoMember(9)> Public Property com_transport As String
    '<ProtoMember(10)> Public Property prix_facture_proforma As Single
    '<ProtoMember(11)> Public Property pickup As Boolean
    '<ProtoMember(12)> Public Property cout_transport As Decimal
    '<ProtoMember(13)> Public Property rowversion As Byte()
    '<ProtoMember(14)> Public Property bolt_seal_number As String
    <ProtoMember(15)> Public Property id_requisition_transport As Integer
End Class