﻿Imports ProtoBuf

<ProtoContract()> <PetaPoco.PrimaryKey("id_commandemachinescrap")>
Partial Public Class commande_machine_scrap
    'Implements IUtilisationResine
    <ProtoMember(1)> Public Property id_commandemachinescrap As Integer
    <ProtoMember(2)> Public Property id_commande As Integer
    <ProtoMember(10)> Public Property id_commande_machine As Integer 'Implements IUtilisationResine.id_commande_machine
    <ProtoMember(3)> Public Property id_machine As Integer
    <ProtoMember(11)> Public Property id_rouleau As Integer

    <ProtoMember(7)> Public Property date_scrap As DateTime 'Implements IUtilisationResine.date_utilisation

    <ProtoMember(4)> Public Property poids_scrap As Double 'Implements IUtilisationResine.qte_lb_utilise

    <ProtoMember(9)> Public Property type_setup As Boolean

    <ProtoMember(12)> Public Property id_type_rejet_dispo As Integer

    <ProtoMember(24)> Public Property rejet_rouleau As Boolean
    <ProtoMember(23)> Public Property actif As Boolean
    <ProtoMember(27)> Public Property date_annule As DateTime?
    <ProtoMember(40)> Public Property id_rsn_rcl As Integer
End Class