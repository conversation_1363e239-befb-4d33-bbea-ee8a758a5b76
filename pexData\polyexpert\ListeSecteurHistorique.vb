﻿Imports ProtoBuf

<ProtoContract()>
<PetaPoco.PrimaryKey("ListeSecteurHistoriqueID")>
Partial Public Class ListeSecteurHistorique
    <ProtoMember(1)> Public Property ListeSecteurHistoriqueID As Integer
    <ProtoMember(2)> Public Property id_secteur As Integer
    <ProtoMember(3)> Public Property date_debut As DateTime
    <ProtoMember(4)> Public Property date_fin As DateTime
    <ProtoMember(5)> Public Property cout_transport As Decimal
    <ProtoMember(6)> Public Property cout_transport_camion As Decimal
    <ProtoMember(7)> Public Property cout_transport_train As Decimal
    <ProtoMember(8)> Public Property cout_transport_bateau As Decimal
    <ProtoMember(9)> Public Property cout_transport_avion As Decimal
    <ProtoMember(10)> Public Property douane_assurance As Decimal
End Class
