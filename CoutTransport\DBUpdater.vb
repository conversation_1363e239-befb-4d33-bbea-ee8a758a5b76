﻿Imports System.Collections.Concurrent
Imports System.Collections.ObjectModel
Imports System.Data.SqlClient
Imports System.IO
Imports System.IO.Compression
Imports System.Net
Imports System.Reflection
Imports System.Threading
Imports Utilities

Module DBUpdater

    Public q_Secteur As ConcurrentQueue(Of (dateCout As DateTime, moyen As Integer, liste As List(Of std_cout_transport_secteur)))
    Public q_Adresse As ConcurrentQueue(Of (dateCout As DateTime, moyen As Integer, liste As List(Of std_cout_transport_adresse)))
    Public q_Client As ConcurrentQueue(Of (dateCout As DateTime, moyen As Integer, liste As List(Of std_cout_transport_client)))
    Public q_Palette As ConcurrentQueue(Of (dateCout As DateTime, moyen As Integer, liste As List(Of std_cout_transport_palette)))

    Public Property StopRequested As Boolean = False

    Private _ppdb As PetaPoco.Database
    Private _lf As LogFile
    Private ReadOnly batchSize As Integer = 10000

#If DEBUG Then
    Private cachePath As String = "G:\WSPex.transport"
#Else
    'Private cachePath As String = "G:\WSPex.transport"
    Private cachePath As String = "C:\WebServices\WSPex\transport"
#End If

    Public Sub MonitorQueues(lf As LogFile)

        _lf = lf

        _lf.Log($"DBUpdater thread started")

        StopRequested = False

        Dim busy As Boolean

        q_Secteur = New ConcurrentQueue(Of (dateCout As DateTime, moyen As Integer, liste As List(Of std_cout_transport_secteur)))
        q_Adresse = New ConcurrentQueue(Of (dateCout As DateTime, moyen As Integer, liste As List(Of std_cout_transport_adresse)))
        q_Client = New ConcurrentQueue(Of (dateCout As DateTime, moyen As Integer, liste As List(Of std_cout_transport_client)))
        q_Palette = New ConcurrentQueue(Of (dateCout As DateTime, moyen As Integer, liste As List(Of std_cout_transport_palette)))

        ' moyen = moyen de transport 0..4
        Dim tSec As (dateCout As DateTime, moyen As Integer, liste As List(Of std_cout_transport_secteur)) = Nothing
        Dim tAdr As (dateCout As DateTime, moyen As Integer, liste As List(Of std_cout_transport_adresse)) = Nothing
        Dim tCli As (dateCout As DateTime, moyen As Integer, liste As List(Of std_cout_transport_client)) = Nothing
        Dim tPal As (dateCout As DateTime, moyen As Integer, liste As List(Of std_cout_transport_palette)) = Nothing

#If DEBUG Then
        '_ppdb = GetDB("Dev") '-todo- get from caller
        _ppdb = GetDB() '-todo- get from caller
#Else
        _ppdb = GetDB() '-todo- get from caller
#End If

        _lf.Log($"Updating DB: {_ppdb.GetConnectionString.RemoveToken("password", ";"c).RemoveToken("User ID", ";"c).RemoveToken("app", ";"c)}")

        Do
            busy = False

            If q_Secteur.TryDequeue(tSec) AndAlso tSec.liste.Count > 0 Then
                busy = True
                DeleteAndInsert2($"std_cout_transport_secteur", tSec.dateCout, tSec.moyen, tSec.liste)
            End If

            If q_Adresse.TryDequeue(tAdr) AndAlso tAdr.liste.Count > 0 Then
                busy = True
                DeleteAndInsert2($"std_cout_transport_adresse", tAdr.dateCout, tAdr.moyen, tAdr.liste)
            End If

            If q_Client.TryDequeue(tCli) AndAlso tCli.liste.Count > 0 Then
                busy = True
                DeleteAndInsert2($"std_cout_transport_client", tCli.dateCout, tCli.moyen, tCli.liste)
            End If

            If q_Palette.TryDequeue(tPal) AndAlso tPal.liste.Count > 0 Then
                busy = True
                DeleteAndInsertPalette2($"std_cout_transport_palette", tPal.moyen, tPal.liste) ' .moyen contient la periode pour les palettes
            End If

            If Not busy Then Thread.Sleep(1000) ' relax a bit

        Loop Until _StopRequested AndAlso q_Secteur.Empty AndAlso q_Adresse.Empty AndAlso q_Client.Empty AndAlso q_Palette.Empty

        q_Secteur = Nothing
        q_Adresse = Nothing
        q_Client = Nothing
        q_Palette = Nothing

        _lf.Log($"DBUpdater thread exiting")

    End Sub


    Private Sub DeleteAndInsert2(Of T As std_cout_transport)(table As String, dateCout As Date, moyen As Integer, actual As List(Of T))

        ' Get previous from disk, if not then get from DB+Normalize
        Dim previous As List(Of T)
        Dim fullpath = $"{cachePath}\{table}_{moyen}_{dateCout:yyyy-MM-dd}.datz"
        If IO.File.Exists(fullpath) Then
            Using rd = File.Open(fullpath, FileMode.Open)
                Using zip = New GZipStream(rd, CompressionMode.Decompress, True)
                    previous = ProtoBuf.Serializer.Deserialize(Of List(Of T))(zip)
                End Using
            End Using
            IO.File.Delete(fullpath) ' remove file so we don't accumulate old cruft
        Else
            previous = _ppdb.Fetch(Of T)($"select * from {table} where date_cout = @0 and id_transport_moyen = @1", dateCout, moyen)
            NormalizeByItem(previous)
        End If

        NormalizeByItem(actual)
        Dim diffs = GetUpdatesAndDeletes(previous, actual)

        Dim changes = diffs.Updates.Count + diffs.Deletes.Count

        If changes > 0 Then

            _lf.Log($"     {table} {dateCout:yyyy-MM-dd} - Updates: {diffs.Updates.Count}   Deletes: {diffs.Deletes.Count}")

            Using tx = _ppdb.GetTransaction

                Dim lstDel = diffs.Updates.Select(Function(x) x.id).ToList
                lstDel.AddRange(diffs.Deletes.Select(Function(x) x.id))
                Dim csvDel = lstDel.ToCSV
                Dim id_name = table.Split("_"c).Last
                Dim sqlDel = $"delete from {table} where id_{id_name} in ({csvDel}) and date_cout = @0 and id_transport_moyen = @1"
                'Log($"Delete SQL = {sql}", fileNamePrefix:=_logPrefix)

                'Using New Chrono(Sub(ms) Log($"          DELETE {table} took {ms} ms", fileNamePrefix:=_logPrefix))
                If Not diffs.InsertsOnly Then
                        _ppdb.Execute(sqlDel, dateCout, moyen)
                    End If
                'End Using

                'Using New Chrono(Sub(ms) Log($"          BulkCopy {table} took {ms} ms", fileNamePrefix:=_logPrefix))
                BulkCopy(diffs.Updates, tx, table, batchSize) ' bulk insert new data
                'End Using

                tx.Complete()

            End Using

        End If

        ' save for next run        
        Using fs = IO.File.Create(fullpath)
            Using zip = New GZipStream(fs, CompressionMode.Compress, True)
                ProtoBuf.Serializer.Serialize(zip, actual)
            End Using
        End Using

        diffs.Deletes.Clear()
        diffs.Updates.Clear()

        actual.Clear()

    End Sub

    Private Sub DeleteAndInsertPalette2(table As String, periode As Integer, actual As List(Of std_cout_transport_palette))

        ' Get previous from disk, if not then get from DB+Normalize
        Dim previous As List(Of std_cout_transport_palette)
        Dim fullpath = $"{cachePath}\{table}_{periode}.datz"
        If IO.File.Exists(fullpath) Then
            Using rd = File.Open(fullpath, FileMode.Open)
                Using zip = New GZipStream(rd, CompressionMode.Decompress, True)
                    previous = ProtoBuf.Serializer.Deserialize(Of List(Of std_cout_transport_palette))(zip)
                End Using
            End Using
            IO.File.Delete(fullpath) ' remove file so we don't accumulate old cruft
        Else
            previous = _ppdb.Fetch(Of std_cout_transport_palette)($"select * from {table} where periode = @0", periode)
            NormalizeByItem(previous)
        End If

        NormalizeByItem(actual)
        Dim diffs = GetUpdatesAndDeletesPalette(previous, actual)

        Dim changes = diffs.Updates.Count + diffs.Deletes.Count

        If changes > 0 Then

            _lf.Log($"     {table} {periode} - Updates: {diffs.Updates.Count}   Deletes: {diffs.Deletes.Count}")

            Using tx = _ppdb.GetTransaction

                Dim lstDel = diffs.Updates.Select(Function(x) x.id_commande_palette).ToList
                lstDel.AddRange(diffs.Deletes.Select(Function(x) x.id_commande_palette))
                Dim csvDel = lstDel.ToCSV
                Dim sqlDel = $"delete from {table} where id_commande_palette in ({csvDel}) and periode = @0"

                'Using New Chrono(Sub(ms) Log($"          DELETE {table} took {ms} ms", fileNamePrefix:=_logPrefix))
                If Not diffs.InsertsOnly Then
                        _ppdb.Execute(sqlDel, periode)
                    End If
                'End Using

                'Using New Chrono(Sub(ms) Log($"          BulkCopy {table} took {ms} ms", fileNamePrefix:=_logPrefix))
                BulkCopy(diffs.Updates, tx, table, batchSize) ' bulk insert new data
                'End Using

                tx.Complete()

            End Using

        End If

        ' save for next run        
        Using fs = IO.File.Create(fullpath)
            Using zip = New GZipStream(fs, CompressionMode.Compress, True)
                ProtoBuf.Serializer.Serialize(zip, actual)
            End Using
        End Using

        diffs.Deletes.Clear()
        diffs.Updates.Clear()

        actual.Clear()

    End Sub

    '-todo- move to PetaPoco itself or a shared library...
    Private ReadOnly ConnectionInfo As PropertyInfo = GetType(SqlConnection).GetProperty("InnerConnection", BindingFlags.NonPublic Or BindingFlags.Instance)

    Private Function GetTransaction(ByVal conn As IDbConnection) As SqlTransaction
        Dim internalConn = ConnectionInfo.GetValue(conn, Nothing)
        Dim currentTransactionProperty = internalConn.[GetType]().GetProperty("CurrentTransaction", BindingFlags.NonPublic Or BindingFlags.Instance)
        Dim currentTransaction = currentTransactionProperty.GetValue(internalConn, Nothing)
        Dim realTransactionProperty = currentTransaction.[GetType]().GetProperty("Parent", BindingFlags.NonPublic Or BindingFlags.Instance)
        Dim realTransaction = realTransactionProperty.GetValue(currentTransaction, Nothing)
        Return CType(realTransaction, SqlTransaction)
    End Function

    ' db connection and transaction are managed by caller
    Public Sub BulkCopy(Of T)(lstObj As List(Of T), pptx As PetaPoco.Transaction, tableName As String, Optional batchSize As Integer = 20000)

        Dim lFrom = 0

        Dim cn = DirectCast(pptx.Database.Connection, SqlConnection)
        Dim tx = GetTransaction(cn)

        Using bulkCopy = New SqlBulkCopy(cn, SqlBulkCopyOptions.KeepIdentity, tx)
            bulkCopy.BatchSize = batchSize
            bulkCopy.DestinationTableName = tableName
            bulkCopy.BulkCopyTimeout = 90
            Try
                Do
                    If lFrom + batchSize > lstObj.Count Then batchSize = lstObj.Count - lFrom
                    Dim tmp1 = lstObj.GetRange(lFrom, batchSize)
                    Dim tmp2 = AsDataTable(tmp1)
                    bulkCopy.WriteToServer(tmp2)
                    lFrom += batchSize
                Loop Until lFrom >= lstObj.Count
            Catch ex As Exception
                Debug.WriteLine(ex.ToString)
                Debug.Assert(False)
                'cn.Close()
            End Try
        End Using

    End Sub

    Private Function GetUpdatesAndDeletes(Of T As std_cout_transport)(previous As List(Of T), actual As List(Of T)) As (Updates As List(Of T), Deletes As List(Of T), InsertsOnly As Boolean)

        Dim lstUpdates = New List(Of T)
        Dim lstDeletes = New List(Of T)
        Dim insertsOnly As Boolean = False

        If previous.Count = 0 Then
            ' no previous data, add all actual.
            lstUpdates.AddRange(actual)
            insertsOnly = True
        Else
            previous = previous.OrderBy(Function(x) x.id).ToList
            actual = actual.OrderBy(Function(x) x.id).ToList

            ' compare matching records
            Dim prev = 0, actu = 0 ' ptr previous, actual
            Do

                Dim oPrev = previous(prev)
                Dim oActu = actual(actu)

CompareObjects:
                ' Check that we are comparing the same records
                If (oPrev.id <> oActu.id) Then

                    ' put correct object in proper list
                    If oPrev.id < oActu.id Then
                        ' previous is deleted
                        lstDeletes.Add(oPrev)
                        prev += 1
                        oPrev = previous(prev)
                    Else
                        ' actual will be inserted
                        lstUpdates.Add(oActu)
                        actu += 1
                        oActu = actual(actu)
                    End If

                    GoTo CompareObjects ' try again until "keys" match
                End If

                If Not ObjectsAreTheSame(oPrev, oActu) Then
                    lstUpdates.Add(oActu)
                End If

                prev += 1
                actu += 1

                If prev >= previous.Count Then
                    ' at the end for previous rows; insert any remaining in actual
                    If actu < actual.Count Then
                        For i = actu To actual.Count - 1
                            lstUpdates.Add(actual(i))
                        Next
                    End If
                    Exit Do
                End If

                If actu >= actual.Count Then
                    ' at the end for actual rows; delete any remaining in previous
                    Debug.Write("") ' never tested
                    If prev < previous.Count Then
                        For i = prev To previous.Count - 1
                            lstDeletes.Add(previous(i))
                        Next
                    End If
                    Exit Do
                End If

            Loop

        End If


        Return (lstUpdates, lstDeletes, insertsOnly)

    End Function

    Private Function GetUpdatesAndDeletesPalette(previous As List(Of std_cout_transport_palette), actual As List(Of std_cout_transport_palette)) As (Updates As List(Of std_cout_transport_palette), Deletes As List(Of std_cout_transport_palette), InsertsOnly As Boolean)

        Dim lstUpdates = New List(Of std_cout_transport_palette)
        Dim lstDeletes = New List(Of std_cout_transport_palette)
        Dim insertsOnly As Boolean = False

        If previous.Count = 0 Then
            ' no previous data, add all actual.
            lstUpdates.AddRange(actual)
            insertsOnly = True
        Else
            previous = previous.OrderBy(Function(x) x.id_commande_palette).ToList
            actual = actual.OrderBy(Function(x) x.id_commande_palette).ToList

            ' compare matching records
            Dim prev = 0, actu = 0 ' ptr previous, actual
            Do

                Dim oPrev = previous(prev)
                Dim oActu = actual(actu)

CompareObjects:
                ' Check that we are comparing the same records
                If (oPrev.id_commande_palette <> oActu.id_commande_palette) Then

                    ' put correct object in proper list
                    If oPrev.id_commande_palette < oActu.id_commande_palette Then
                        ' previous is deleted
                        lstDeletes.Add(oPrev)
                        prev += 1
                        oPrev = previous(prev)
                    Else
                        ' actual will be inserted
                        lstUpdates.Add(oActu)
                        actu += 1
                        oActu = actual(actu)
                    End If

                    GoTo CompareObjects ' try again until "keys" match
                End If

                If Not ObjectsAreTheSame(oPrev, oActu) Then
                    lstUpdates.Add(oActu)
                End If

                prev += 1
                actu += 1

                If prev >= previous.Count Then
                    ' at the end for previous rows; insert any remaining in actual
                    If actu < actual.Count Then
                        For i = actu To actual.Count - 1
                            lstUpdates.Add(actual(i))
                        Next
                    End If
                    Exit Do
                End If

                If actu >= actual.Count Then
                    ' at the end for actual rows; delete any remaining in previous
                    Debug.Write("") ' never tested
                    If prev < previous.Count Then
                        For i = prev To previous.Count - 1
                            lstDeletes.Add(previous(i))
                        Next
                    End If
                    Exit Do
                End If

            Loop

        End If

        Return (lstUpdates, lstDeletes, insertsOnly)

    End Function

End Module
