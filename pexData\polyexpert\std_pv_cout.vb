﻿Imports ProtoBuf

<PetaPoco.PrimaryKey("ID_STD_PV_COUT")>
<ProtoContract()>
Partial Public Class std_pv_cout
    <ProtoMember(1)> Public Property id_std_pv_cout As Integer
    <ProtoMember(2)> Public Property pv As String
    <ProtoMember(3)> Public Property date_debut As DateTime
    <ProtoMember(4)> Public Property date_fin As DateTime
    <ProtoMember(5)> Public Property cout As Decimal
    <ProtoMember(6)> Public Property id_std_categorie As Integer
    'Public Property tmp As Integer?
    'Public Property sous_marche_auto As String
    <ProtoMember(9)> Public Property id_pv_std As Integer
End Class