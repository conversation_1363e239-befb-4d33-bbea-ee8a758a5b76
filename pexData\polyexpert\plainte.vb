﻿Imports ProtoBuf

<PetaPoco.PrimaryKey("ID_PLAINTE")>
<ProtoContract()>
Partial Public Class plainte
    <ProtoMember(1)> Public Property id_plainte As Integer
    'Public Property date_plainte As DateTime?
    'Public Property id_client As Integer?
    <ProtoMember(4)> Public Property id_commande As Integer
    'Public Property plainte_fondee As Boolean
    'Public Property defaut_client As String
    'Public Property desc_generale As String
    'Public Property desc_technique As String
    'Public Property id_type_plainte As Integer?
    'Public Property id_statut_plainte As Integer?
    'Public Property id_statut_ac As Integer?
    'Public Property id_priorite_plainte As Integer?
    'Public Property quantite_defaut As Integer?
    'Public Property quantite_defaut_pe_analyse As Integer?
    'Public Property id_plainte_categorie_defaut As Integer?
    'Public Property diagnostic_preliminaire As String
    'Public Property peut_reproduire_autre_cmd As Boolean
    'Public Property bloquer_expedition As Boolean
    'Public Property bloquer_pv_expedition As Boolean
    'Public Property analyse_primaire As String
    'Public Property id_employe_analyse_primaire As Integer?
    'Public Property date_analyse_primaire As DateTime?
    'Public Property analyse_secondaire As String
    'Public Property analyse_secondaire_raison As String
    'Public Property id_employe_analyse_secondaire As Integer?
    'Public Property date_analyse_secondaire As DateTime?
    'Public Property conclusion_analyse As String
    'Public Property echantillon_necessaire As Boolean
    'Public Property date_reception_echantillon As DateTime?
    'Public Property date_remise_echantillon_qc As DateTime?
    'Public Property visite_requise As Boolean
    'Public Property desc_complementaire_echantillon As String
    'Public Property desc_complementaire_exterieur As String
    'Public Property commande_remplacement_a_emettre As Boolean
    'Public Property retour_materiel As Boolean
    'Public Property commentaire_retour_materiel As String
    'Public Property date_retour As DateTime?
    'Public Property quantite_retour As Integer?
    'Public Property id_transport_retour As Integer?
    'Public Property autre_info_retour As String
    'Public Property date_reception_retour As DateTime?
    'Public Property quantite_reception As Integer?
    'Public Property quantite_reception_non_converti As Integer?
    'Public Property quantite_reception_imprime As Integer?
    'Public Property quantite_reception_converti As Integer?
    'Public Property info_entente_fournisseur As String
    'Public Property montant_entente_fournisseur As Single?
    'Public Property retour_fournisseur As Boolean
    'Public Property envoyer_recyclage As Boolean
    'Public Property email_nouvelle_plainte_envoye As Boolean
    'Public Property email_diagnostic_envoye As Boolean
    'Public Property email_analyse_envoye As Boolean
    'Public Property email_echantillon_necessaire_envoye As Boolean
    'Public Property email_visite_requise_envoye As Boolean
    'Public Property email_commande_remplacement_envoye As Boolean
    'Public Property email_retour_materiel_envoye As Boolean
    'Public Property date_retour_demande As DateTime?
    'Public Property date_statut_entente As DateTime?
    'Public Property prix_approx As Single?
    'Public Property autre_cout As Single?
    'Public Property id_devise As Integer
    'Public Property prix_total As Single?
    'Public Property total_devise As Single?
    'Public Property total_can As Single?
    'Public Property id_disposition_materiel As Integer?
    'Public Property desc_disposition As String
    'Public Property id_plainte_cause As Integer?
    'Public Property id_machine As Integer?
    'Public Property no_rouleau As String
    'Public Property ajustement_credit_client As Decimal?
    'Public Property id_technicien_constat As Integer?
    'Public Property cause_validee As Boolean?
    'Public Property id_plainte_defaut_client As Integer?
    'Public Property defaut_client_old As String
    'Public Property iscontrolable As Integer?
    'Public Property hasvisiterequise As Integer?
    'Public Property hasretourmateriel As Integer?
    'Public Property id_employe_analyse_complementaire As Integer?
    'Public Property date_analyse_complementaire As DateTime?
    'Public Property id_plaintelocalisationtype As Integer?
    'Public Property ac_recommandation As String
    'Public Property ac_date_rcmd As DateTime?
    'Public Property ac_id_empl_rcmd As Integer?
    'Public Property ac_action_0 As String
    'Public Property ac_action_1 As String
    'Public Property ac_action_2 As String
    'Public Property ac_date_act_0 As DateTime?
    'Public Property ac_date_act_1 As DateTime?
    'Public Property ac_date_act_2 As DateTime?
    'Public Property ac_id_empl_act_0 As Integer?
    'Public Property ac_id_empl_act_1 As Integer?
    'Public Property ac_id_empl_act_2 As Integer?
    'Public Property ac_id_eval_0 As Integer?
    'Public Property ac_id_eval_1 As Integer?
    'Public Property ac_id_eval_2 As Integer?
    'Public Property ac_date_eval_0 As DateTime?
    'Public Property ac_date_eval_1 As DateTime?
    'Public Property ac_date_eval_2 As DateTime?
    'Public Property ac_id_empl_eval_0 As Integer?
    'Public Property ac_id_empl_eval_1 As Integer?
    'Public Property ac_id_empl_eval_2 As Integer?
    'Public Property credit_repartis_can As Decimal?
    'Public Property email_conclusion_plainte_envoye As Boolean
    'Public Property email_conclusion_ac_envoye As Boolean
    'Public Property r_id_adresse_retour As Integer?
    'Public Property r_adresse_retour As String
    'Public Property r_qte_req_analyse As Integer?
    'Public Property r_personne_contact As String
    'Public Property r_date_constat As DateTime?
    'Public Property r_exped_echantillon_id As Integer?
    'Public Property r_date_echantillon As DateTime?
    'Public Property r_adresse_retour_rue As String
    'Public Property r_adresse_retour_ville As String
    'Public Property r_adresse_retour_province As String
    'Public Property r_adresse_retour_code_postal As String
    'Public Property r_adresse_retour_pays As String
    'Public Property r_technicien_materiel_non_converti_id As Integer?
    'Public Property r_date_technicien_materiel_non_converti As DateTime?
    'Public Property r_id_disposition_materiel_imprimee As Integer?
    'Public Property r_technicien_materiel_imprimee_id As Integer?
    'Public Property r_date_technicien_materiel_imprimee As DateTime?
    'Public Property r_id_disposition_materiel_converti As Integer?
    'Public Property r_technicien_materiel_converti_id As Integer?
    'Public Property r_date_technicien_materiel_converti As DateTime?
    'Public Property ac_date_act_vise_0 As DateTime?
    'Public Property ac_date_act_vise_1 As DateTime?
    'Public Property ac_date_act_vise_2 As DateTime?
End Class