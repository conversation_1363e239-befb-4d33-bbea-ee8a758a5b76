﻿Imports ProtoBuf

<ProtoContract()>
<PetaPoco.PrimaryKey("ID_TRANSPORT")>
Partial Public Class transport
    <ProtoMember(1)> Public Property id_transport As Integer
    '<ProtoMember(2)> Public Property nom As String
    '<ProtoMember(3)> Public Property description As String
    '<ProtoMember(4)> Public Property exportation As Boolean
    '<ProtoMember(5)> Public Property courtier As String
    '<ProtoMember(6)> Public Property dans_notre_camion As Boolean
    '<ProtoMember(7)> Public Property dans_livre As Boolean
    '<ProtoMember(8)> Public Property dans_pickup As Boolean
    '<ProtoMember(9)> Public Property desc_dyn As String
    <ProtoMember(10)> Public Property fact_client As Boolean
    '<ProtoMember(11)> Public Property recyclage As Boolean
    '<ProtoMember(12)> Public Property client_paie_transport As Boolean
    '<ProtoMember(13)> Public Property rowversion As Byte()
End Class