﻿Imports ProtoBuf

<PetaPoco.PrimaryKey("id_rouleau")>
<ProtoContract>
Partial Public Class rouleau

    <ProtoMember(1)> Public Property id_rouleau As Integer
    <ProtoMember(2)> Public Property id_commande As Integer
    <ProtoMember(29)> Public Property id_commande_machine As Integer
    <ProtoMember(15)> Public Property id_machine As Integer
    <ProtoMember(3)> Public Property id_palette As Integer

    <ProtoMember(9)> Public Property date_rouleau As DateTime

    <ProtoMember(35)> Public Property poids_net As Double
    <ProtoMember(4)> Public Property poids As Double
    <ProtoMember(12)> Public Property poids_core As Double

    <ProtoMember(11)> Public Property id_raison As Integer
    <ProtoMember(20)> Public Property id_conformite As Integer
    '<ProtoMember(26)> Public Property date_non_conf As DateTime?

    <PetaPoco.Ignore> Public Property totProd As totauxProduction

End Class
