﻿Public Class totauxProduction

    ' r01 = rouleaux avec id_conformite = 0 ou 1
    Public Property rou01_count As Integer
    Public Property rou01_qte As Decimal
    Public Property rou01_cou_can As Decimal
    Public Property rou01_cou_us As Decimal
    Public Property rou01_cou_std_us As Decimal '* Cout standard us.
    Public Property rou01_cou_lst_us As Decimal '* Cout liste us.

    ' r2 = rouleaux avec id_conformite = 2
    Public Property rou2_qte As Decimal
    Public Property rou2_cou_scr_can As Decimal
    Public Property rou2_cou_scr_us As Decimal

    ' r3 = rouleaux avec id_conformite = 3
    Public Property rou3_qte As Decimal
    Public Property rou3_cou_scr_can As Decimal
    Public Property rou3_cou_scr_us As Decimal

    ' r23 = rouleaux avec id_conformite = 2 ou 3
    Public Property rou23_qte As Decimal
    Public Property rou23_cou_can As Decimal
    Public Property rou23_cou_us As Decimal
    Public Property rou23_cou_rep_can As Decimal ' cout reprocess can
    Public Property rou23_cou_rep_us As Decimal
    Public Property rou23_cou_ext_can As Decimal ' cout extrusion can
    Public Property rou23_cou_ext_us As Decimal

    ' scr = scrap
    Public Property scrap_qte As Decimal
    Public Property scrap_cou_can As Decimal ' cout CAN
    Public Property scrap_cou_us As Decimal  ' cout US
    Public Property scrap_cou_rep_can As Decimal ' reprocess CAN
    Public Property scrap_cou_ext_can As Decimal ' extrusion CAN (var. globale)
    Public Property scrap_cou_rep_us As Decimal ' reprocess US
    Public Property scrap_cou_ext_us As Decimal ' extrusion US (var. globale)

    ' set = setup
    Public Property setup_qte As Decimal
    Public Property setup_cou_scr_can As Decimal
    Public Property setup_cou_scr_us As Decimal
    ' test
    Public Property setup_budget As Decimal ' budget_setup_lbs réparti par rouleau

    ' vrac = vrac!!
    Public Property vrac_qte As Decimal
    Public Property vrac_cou_scr_can As Decimal
    Public Property vrac_cou_scr_us As Decimal

End Class
