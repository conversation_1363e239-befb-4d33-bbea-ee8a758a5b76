﻿Imports System.Collections.Concurrent
Imports NetMQ.Core.Transports
Imports Utilities

Public Module pexReportACommandeMachine

    Public ccdCM As New ConcurrentDictionary(Of Long, groupCM)
    Public dicPexCM As Dictionary(Of Long, pexCommandeMachine)
    Public lstPexCM As List(Of pexCommandeMachine)

    Public Sub CleanMemory()
        lstPexCM.Clear()
        lstPexCM = Nothing
        dicPexCM.Clear()
        dicPexCM = Nothing
        ccdCM.Clear()
        ccdCM = Nothing
    End Sub

    Public Sub BuildCommandeMachine()

#If 0 Then
        Log("     ************* SKIPPING spAjusterStdPvCout ************** ")
#Else
        Try

            If INI.ReadKey("Config", "spAjusterStdPvCout", "0") = "1" Then
                ' Ancien calcul - va crasher si le trigger "readonly" n'est pas désactivé sur la table STD_PV_COUT_ARCHIVE
                Using New Chrono(Sub(ms) Log($"spAjusterStdPvCout - elapsed: {ms}ms"), Sub() Log("spAjusterStdPvCout started..."))
                    ppdb_report.OneTimeCommandTimeout = 300 ' seconds
                    ppdb_report.Execute("execute spAjusterStdPvCout")
                End Using
            Else
                ' Pour l'instant, on continue à rouler spMAJVieuxPrixResine
                Using New Chrono(Sub(ms) Log($"spMAJVieuxPrixResine - elapsed: {ms}ms"), Sub() Log("spMAJVieuxPrixResine started..."))
                    ppdb_report.OneTimeCommandTimeout = 300 ' seconds
                    ppdb_report.Execute("execute spMAJVieuxPrixResine")
                End Using
                ' spAjusterStdPvCout remplacé par service CalculSTDPVCoutActualisable
                Using New Chrono(Sub(ms) Log($"CalculSTDPVCoutActualisable - elapsed: {ms}ms"), Sub() Log("CalculSTDPVCoutActualisable() started..."))
                    CalculSTDPVCoutActualisable()
                End Using
            End If

        Catch ex As Exception
            Log($"{ex}") ' we log and continue; PV cost do not change enough to warrant stopping the process here.
        End Try
#End If
        Using New Chrono(Sub(ms) Log($"load CM - elapsed: {ms}ms"))
            ccdCM = New ConcurrentDictionary(Of Long, groupCM)(lstCM.ToDictionary(Function(k) GetDualIdKey(k.id_commande_machine, k.id_commande), Function(v) GetGroupCM(v)))
            dicPexCM = New Dictionary(Of Long, pexCommandeMachine)
            lstPexCM = New List(Of pexCommandeMachine)
        End Using

        ' mettre les rouleaux dans leurs grpCM
        Dim gcm As groupCM = Nothing
        Dim gcmd As groupC = Nothing
        Using New Chrono(Sub(ms) Log($"rouleau - elapsed: {ms}ms"))
            For Each rou In rouleaux
                If rou.id_commande_machine > 0 Then '-todo- fix integrity problem for rouleaux with id_cmd_mac <= 0


                    If ccdCM.TryGetValue(GetDualIdKey(rou.id_commande_machine, rou.id_commande), gcm) Then


                        Debug.Assert(rou.id_commande_machine = gcm.cmdMac.id_commande_machine) ' -todo- review and fix
                        gcm.rouleaux.Add(GetGroupInfoRouleau(rou, gcm, lstSVG))
                    Else
                        'Debug.Assert(False)
                    End If
                End If
            Next
        End Using

        ' mettre la scrap dans leurs CM
        Using New Chrono(Sub(ms) Log($"cms - elapsed: {ms}ms"))
            Dim missing = 0
            For Each scr In scrap
                If Not ccdCM.TryGetValue(GetDualIdKey(scr.id_commande_machine, scr.id_commande), gcm) Then
                    missing += 1

                    'Debug.Assert(scr.id_commande_machine <> 260377)
                    'Debug.Assert(scr.id_commande <> 801747)
                    'Debug.Assert(scr.id_commande <> 801855)

                    Dim lst = lstCM.Where(Function(x) x.id_commande_machine = scr.id_commande_machine).ToList ' -todo- make lookup faster?
                    'If lst.Count > 1 Then Debug.Write("")
                    Dim cm = lst.FirstOrDefault

                    Debug.Assert(cm IsNot Nothing)

                    gcm = GetGroupCM(New commande_machine With {
                                     .id_commande_machine = scr.id_commande_machine,
                                     .id_commande = scr.id_commande,
                                     .id_machine = If(cm?.id_machine, 0),
                                     .date_debut_setup = If(cm?.date_debut_setup, Nothing),
                                     .date_fin_setup = If(cm?.date_fin_setup, Nothing),
                                     .date_debut_commande = If(cm?.date_debut_commande, Nothing),
                                     .date_fin_commande = If(cm?.date_fin_commande, Nothing),
                                     .cedule_order = If(cm?.cedule_order, 0),
                                     .temps_setup_lb = 0
                                     })
                    ccdCM.AddOrUpdate(GetDualIdKey(scr.id_commande_machine, scr.id_commande), gcm, Function(id, obj) gcm)
                End If
                gcm.scrap.Add(GetGroupInfoScrap(scr, gcm, lstSVG))
            Next
            Log($"cms - missing CM: {missing}")
        End Using

        Log($"Processing production (prod1)...")

        Using New Chrono(Sub(ms) Log($"prod1 - elapsed: {ms}ms"))

            Dim grpScrapRouleau = scrap_rouleau.GroupBy(Function(x) x.id_rouleau).ToDictionary(Function(k) k.Key, Function(v) v.ToList)

            For Each gcm In ccdCM.Values
#If DEBUG Then
                Debug.Assert(COMMANDE_MACHINE_CHECK <= 0 OrElse gcm.cmdMac.id_commande_machine <> COMMANDE_MACHINE_CHECK)
#End If
                Dim tp = New totauxProduction
                gcm.totProd = tp
                tp.setup_budget = gcm.cmdMac.temps_setup_lb ' test

                For Each gr In gcm.rouleaux
                    Dim r = gr.rou
                    Dim poids_net = CDec(r.poids_net)
                    Select Case r.id_conformite

                        Case 0, 1 ' conf, n/c accepté
                            tp.rou01_count += 1
                            tp.rou01_qte += poids_net
                            tp.rou01_cou_can += poids_net * gr.cmdStr.cout
                            tp.rou01_cou_us += poids_net * gr.cmdStr.cout_us
                            tp.rou01_cou_std_us += poids_net * gr.cmdStr.cout_std_us
                            tp.rou01_cou_lst_us += poids_net * gr.cmdStr.cout_liste_us

                        Case 2 ' nch -- en attente
                            tp.rou2_qte += poids_net
                            If gr.coutsRsnScrap IsNot Nothing Then
                                tp.rou2_cou_scr_can += poids_net * gr.coutsRsnScrap.cout_can
                                tp.rou2_cou_scr_us += poids_net * gr.coutsRsnScrap.cout_us
                            Else
                                Debug.Write("") '-gg-
                            End If


                            tp.rou23_qte += poids_net
                            tp.rou23_cou_can += poids_net * gr.cmdStr.cout
                            tp.rou23_cou_us += poids_net * gr.cmdStr.cout_us



                            tp.rou23_cou_ext_can += poids_net * gr.varGlb.extrusion
                            tp.rou23_cou_ext_us += poids_net * gr.varGlb.extrusion / If(gr.tauxChange.taux = 0D, 1D, gr.tauxChange.taux)

                            ' Remis en fonction 2024-07-10 - on ne sait pas pourquoi se fut enlevé.
                            If gcm.id_rsn_scr > 0 Then
                                tp.rou23_cou_rep_can += poids_net * gr.varGlb.reprocess
                                tp.rou23_cou_rep_us += poids_net * gr.varGlb.reprocess / If(gr.tauxChange.taux = 0D, 1D, gr.tauxChange.taux)
                            End If

                        Case 3 ' ncr -- rejeté

                            Dim poids_scrap = 0D
                            Dim poids_scrap_3_rejete = 0D
                            Dim id_rsn_scrap_2 = -1
                            'Dim lst_cmd_mac_scrap As List(Of commande_machine_scrap) = Nothing

                            If grpScrapRouleau.ContainsKey(r.id_rouleau) Then
                                'lst_cmd_mac_scrap = grpScrapRouleau(r.id_rouleau)
                                'poids_scrap_tot = CDec(lst_cmd_mac_scrap.Sum(Function(x) x.poids_scrap))
                                'tp.rou3_qte += poids_scrap_tot
                                Dim counter As Integer = 0
                                For Each cms In grpScrapRouleau(r.id_rouleau) 'lst_cmd_mac_scrap
                                    If r.id_rouleau = 3880243 Then
                                        Debug.Write("")
                                    End If

                                    'poids_scrap = CDec(cms.poids_scrap)
                                    poids_scrap += CDec(cms.poids_scrap)
                                    poids_scrap_3_rejete = CDec(cms.poids_scrap)
                                    tp.rou3_qte += poids_scrap_3_rejete
                                    tp.rou3_cou_scr_can += poids_scrap_3_rejete * gr.coutsRsnScrap.cout_can
                                    tp.rou3_cou_scr_us += poids_scrap_3_rejete * gr.coutsRsnScrap.cout_us

                                    If cms.id_rsn_rcl > 0 Then
                                        tp.rou23_cou_rep_can += poids_scrap_3_rejete * gr.varGlb.reprocess
                                        tp.rou23_cou_rep_us += poids_scrap_3_rejete * gr.varGlb.reprocess / If(gr.tauxChange.taux = 0D, 1D, gr.tauxChange.taux)
                                    End If

                                Next

                            Else
                                Debug.Write("") ' pas de scrap pour un rouleau non-conf rejete?
                            End If


#If DEBUG Then
                            If poids_scrap <= 0 Then Debug.Write("")
#End If

                            tp.rou23_qte += poids_scrap
                            tp.rou23_cou_can += poids_scrap * gr.cmdStr.cout
                            tp.rou23_cou_us += poids_scrap * gr.cmdStr.cout_us

                            tp.rou23_cou_ext_can += poids_scrap * gr.varGlb.extrusion
                            tp.rou23_cou_ext_us += poids_scrap * gr.varGlb.extrusion / If(gr.tauxChange.taux = 0D, 1D, gr.tauxChange.taux)



                    End Select
                Next


                For Each gs In gcm.scrap
                    Dim s = gs.cmd_mac_scr
                    Dim poids_scrap = CDec(s.poids_scrap)

                    If s.type_setup Then
                        tp.setup_qte += poids_scrap
                        tp.setup_cou_scr_can += poids_scrap * gs.rsn_scr_cou_his.cout_can
                        tp.setup_cou_scr_us += poids_scrap * gs.rsn_scr_cou_his.cout_us
                    Else
                        tp.vrac_qte += poids_scrap
                        tp.vrac_cou_scr_can += poids_scrap * gs.rsn_scr_cou_his.cout_can
                        tp.vrac_cou_scr_us += poids_scrap * gs.rsn_scr_cou_his.cout_us
                    End If

                    tp.scrap_qte += poids_scrap
                    tp.scrap_cou_can += poids_scrap * gs.cmd_str.cout
                    tp.scrap_cou_us += poids_scrap * gs.cmd_str.cout_us

                    tp.scrap_cou_ext_can += poids_scrap * gs.std_var_glb.extrusion
                    tp.scrap_cou_ext_us += poids_scrap * gs.std_var_glb.extrusion / If(gs.tau_chg.taux = 0D, 1D, gs.tau_chg.taux)

                    'If gcm.cmdMac.date_debut_commande < DAT_CHANGEMENT_CALCUL_SCRAP Then
                    ' Remis en fonction 2024-07-10 - on ne sait pas pourquoi se fut enlevé.
                    'If gcm.id_rsn_scr > 0 Then
                    If gs.cmd_mac_scr.id_rsn_rcl > 0 Then
                        tp.scrap_cou_rep_can += poids_scrap * gs.std_var_glb.reprocess
                        tp.scrap_cou_rep_us += poids_scrap * gs.std_var_glb.reprocess / If(gs.tau_chg.taux = 0D, 1D, gs.tau_chg.taux)
                    End If
                    'End If
                Next
            Next
        End Using


        Log($"Répartition scrap (prod2)...")
        Using New Chrono(Sub(ms) Log($"prod2 - elapsed: {ms}ms"))
            For Each gcm In ccdCM.Values

                If gcm.totProd.rou01_count > 0 Then
                    ' il existe 1+ rouleau(x) expédiable(s)... 
                    Dim totExped = gcm.totProd.rou01_qte

                    For Each gr In gcm.rouleaux
                        Dim r = gr.rou
                        If r.id_conformite < 2 Then
                            Dim ratio = CDec(r.poids_net / totExped)
                            Dim tp = SplitTotProd(gcm.totProd, ratio)
                            gr.rou.totProd = tp
                        End If
                    Next

                    gcm.totProd = Nothing ' scrap répartie sur les rouleaux expédiables

                End If

            Next
        End Using

        Log($"Création pexCM...")
        Using New Chrono(Sub(ms) Log($"pexCM - elapsed: {ms}ms"))
            For Each gcm In ccdCM.Values

                Dim key = 0L
                Dim pcm As pexCommandeMachine = Nothing

                Dim cm = gcm.cmdMac

                If gcm.totProd Is Nothing Then
                    ' process rouleau expediables
                    For Each gr In gcm.rouleaux
                        If gr.rou.totProd IsNot Nothing Then
                            Dim r = gr.rou
                            pcm = GetPexCM(dicPexCM, gcm.cmdMac, r) ' ajoute au dictionnaire, ou retourne pcm existant
                            AddTotProdToPexCM(pcm, gr.rou.totProd)
                        End If
                    Next
                Else
                    ' pas de production expediable, on crée un record pour la scrap			
                    pcm = GetPexCM(dicPexCM, cm)
                    AddTotProdToPexCM(pcm, gcm.totProd)
                End If

                lstPexCM.Add(pcm)

                ' do totaux/calc on pcm
                pcm.Machine = If(pcm.MachineID > 0, dicMachine(pcm.MachineID).nom, Nothing)
                pcm.ReelMachineMontantCan = GetMachineCostForRun(gcm.cmdMac)

                Dim moiModFinal = GetMoiModCostsForRun(gcm.cmdMac)
                pcm.REELMoiMontantCan = Math.Round(moiModFinal.Moi, 2, MidpointRounding.AwayFromZero)
                pcm.REELModMontantCan = Math.Round(moiModFinal.[Mod], 2, MidpointRounding.AwayFromZero)

                Dim cadPerUsd As Decimal = GetFxCadPerUsdForRun(cm)
                pcm.REELMachineMontantUs = Math.Round(Div0(pcm.ReelMachineMontantCan, cadPerUsd), 2, MidpointRounding.AwayFromZero)
                pcm.REELModMontantUs = Math.Round(Div0(pcm.REELModMontantCan, cadPerUsd), 2, MidpointRounding.AwayFromZero)
                pcm.REELMoiMontantUs = Math.Round(Div0(pcm.REELMoiMontantCan, cadPerUsd), 2, MidpointRounding.AwayFromZero)

                pcm.DateDebutSetup = NDate(cm.date_debut_setup)
                pcm.DateFinSetup = NDate(cm.date_fin_setup)
                pcm.DateDebutCommande = NDate(cm.date_debut_commande)
                pcm.DateFinCommande = NDate(cm.date_fin_commande)
                pcm.BudgetSetupLb = cm.temps_setup_lb
                pcm.OrdreCedule = cm.cedule_order

                Dim tp = pcm.totProd

                pcm.REELResineCanMontantLbs = Div0(tp.rou01_cou_can, tp.rou01_qte)
                pcm.REELResineUsMontantLbs = Div0(tp.rou01_cou_us, tp.rou01_qte)
                pcm.REELResineTauxUs = Div0(pcm.REELResineCanMontantLbs, pcm.REELResineUsMontantLbs)

                pcm.REELResineSTDUsMontantLbs = Div0(tp.rou01_cou_std_us, tp.rou01_qte)
                pcm.REELResineListeUsMontantLbs = Div0(tp.rou01_cou_lst_us, tp.rou01_qte)

                pcm.REELResineScrapCanMontantLbs = Div0((tp.rou23_cou_can + tp.scrap_cou_can), (tp.rou23_qte + tp.scrap_qte))
                pcm.REELResineScrapUsMontantLbs = Div0((tp.rou23_cou_us + tp.scrap_cou_us), (tp.rou23_qte + tp.scrap_qte))

                pcm.REELResineScrapTauxUs = Div0(pcm.REELResineScrapCanMontantLbs, pcm.REELResineScrapUsMontantLbs)

                pcm.REELProductionExpediable = tp.rou01_qte
                pcm.REELNbRouleauExpediable = tp.rou01_count

                pcm.REELRejetSetup = tp.setup_qte
                pcm.REELRejetSetupCan = tp.setup_cou_scr_can
                pcm.REELRejetSetupUs = tp.setup_cou_scr_us

                pcm.REELRejetVrac = tp.vrac_qte
                pcm.REELRejetVracCan = tp.vrac_cou_scr_can
                pcm.REELRejetVracUs = tp.vrac_cou_scr_us

                pcm.REELRejetNCR = tp.rou3_qte
                pcm.REELRejetNCRCan = tp.rou3_cou_scr_can
                pcm.REELRejetNCRUs = tp.rou3_cou_scr_us

                pcm.REELRejetNCH = tp.rou2_qte
                pcm.REELRejetNCHCan = tp.rou2_cou_scr_can
                pcm.REELRejetNCHUs = tp.rou2_cou_scr_us

                pcm.REELRejetTotalExtrusionCan = tp.rou23_cou_ext_can + tp.scrap_cou_ext_can
                pcm.REELRejetTotalExtrusionUs = tp.rou23_cou_ext_us + tp.scrap_cou_ext_us
                pcm.REELRejetTotalReprocessCan = tp.rou23_cou_rep_can + tp.scrap_cou_rep_can
                pcm.REELRejetTotalReprocessUs = tp.rou23_cou_rep_us + tp.scrap_cou_rep_us

                ' Champs dérivés
                pcm.REELRejetTotal = pcm.REELRejetSetup + pcm.REELRejetVrac + pcm.REELRejetNCH + pcm.REELRejetNCR
#If DEBUG Then
                Debug.Assert(COMMANDE_MACHINE_CHECK <= 0 OrElse pcm.CommandeMachineID <> COMMANDE_MACHINE_CHECK)
#End If
                pcm.REELRejetTotalCan = pcm.REELRejetSetupCan + pcm.REELRejetVracCan + pcm.REELRejetNCHCan + pcm.REELRejetNCRCan

                pcm.REELRejetTotalUs = pcm.REELRejetSetupUs + pcm.REELRejetVracUs + pcm.REELRejetNCHUs + pcm.REELRejetNCRUs

                pcm.REELProductionBrute = pcm.REELProductionExpediable + pcm.REELRejetTotal
                pcm.REELRejetPourcentage = Div0(pcm.REELRejetTotal, pcm.REELProductionBrute)

                ' Description du calcul: NOTE CE CODE EST A DEUX ENDROITS
                ' A) on prend le cout de la scrap basé sur la strucure (cout réel de la scrap selon les résines du mélange)
                ' B) on ajoute le cout du reprocess et de l'extrusion (couts fixes selon std_variable_globale)
                ' C) on soustrait le cout de la résine scrap (la valeur que l'on attribue a ce qui reste après le reprocess)
                ' D) on obtient ainsi combien nous a couté la scrap produit (cout initial selon qte*résine + processus - valeur après récupération)

                'If pcm.DateDebutCommande < DAT_CHANGEMENT_CALCUL_SCRAP Then
                ' enlevé 2024-07-10
                pcm.REELScrapCanMontant = (pcm.REELRejetTotal * pcm.REELResineScrapCanMontantLbs) _
                                         + pcm.REELRejetTotalReprocessCan _
                                         + pcm.REELRejetTotalExtrusionCan _
                                         - pcm.REELRejetTotalCan

                pcm.REELScrapCanMontantLbs = Div0(pcm.REELScrapCanMontant, pcm.REELProductionExpediable)


                pcm.REELScrapUsMontant = (pcm.REELRejetTotal * pcm.REELResineScrapUsMontantLbs) _
                                        + pcm.REELRejetTotalReprocessUs _
                                        + pcm.REELRejetTotalExtrusionUs _
                                        - pcm.REELRejetTotalUs

                pcm.REELScrapUsMontantLbs = Div0(pcm.REELScrapUsMontant, pcm.REELProductionExpediable)


                Dim cmd As commande = Nothing
                If dicCommande IsNot Nothing AndAlso dicCommande.TryGetValue(cm.id_commande, cmd) Then
                    pcm.RnDProject = cmd.R_D

                    Dim tsId As Integer = If(cmd IsNot Nothing, cmd.ID_TYPE_SAMPLE.GetValueOrDefault(0), 0)
                    pcm.TypeSampleID = tsId
                    If tsId > 0 AndAlso dicTypeSample IsNot Nothing Then
                        Dim ts As type_sample = Nothing
                        If dicTypeSample.TryGetValue(tsId, ts) AndAlso ts IsNot Nothing Then
                            pcm.TypeSampleCode = ts.code
                            pcm.TypeSampleDescription = ts.description
                        Else
                            pcm.TypeSampleCode = Nothing
                            pcm.TypeSampleDescription = Nothing
                        End If
                    Else
                        pcm.TypeSampleCode = Nothing
                        pcm.TypeSampleDescription = Nothing
                    End If

                    Dim hasCmdPriceCan As Boolean = False
                    If cmd IsNot Nothing Then
                        Dim cmdMontantLbs As Decimal = If(cmd.prix_lbs = 0D, 0D, CDec(cmd.prix_lbs + cmd.surcharge))
                        hasCmdPriceCan = (cmdMontantLbs > 0D)
                    End If

                    Dim parenthesesCan As Decimal = If(hasCmdPriceCan, 0D, pcm.REELProductionExpediable * pcm.REELResineCanMontantLbs)
                    pcm.REELCoutTotalResineCan = pcm.REELScrapCanMontant + parenthesesCan

                    Dim parenthesesUs As Decimal = If(hasCmdPriceCan, 0D, pcm.REELProductionExpediable * pcm.REELResineUsMontantLbs)
                    pcm.REELCoutTotalResineUs = pcm.REELScrapUsMontant + parenthesesUs
                Else
                    pcm.RnDProject = Nothing
                    pcm.TypeSampleID = 0
                    pcm.TypeSampleCode = Nothing
                    pcm.TypeSampleDescription = Nothing

                    Dim parenthesesCan As Decimal = pcm.REELProductionExpediable * pcm.REELResineCanMontantLbs
                    pcm.REELCoutTotalResineCan = pcm.REELScrapUsMontant + parenthesesCan

                    Dim parenthesesUs As Decimal = pcm.REELProductionExpediable * pcm.REELResineUsMontantLbs
                    pcm.REELCoutTotalResineUs = pcm.REELScrapUsMontant + parenthesesUs
                End If


            Next
        End Using


    End Sub

    Sub AddTotProdToPexCM(pcm As pexCommandeMachine, tp As totauxProduction)

        If pcm.totProd Is Nothing Then pcm.totProd = New totauxProduction

        Dim ptp = pcm.totProd

        ptp.rou01_count += tp.rou01_count
        ptp.rou01_qte += tp.rou01_qte
        ptp.rou01_cou_can += tp.rou01_cou_can
        ptp.rou01_cou_us += tp.rou01_cou_us
        ptp.rou01_cou_std_us += tp.rou01_cou_std_us
        ptp.rou01_cou_lst_us += tp.rou01_cou_lst_us

        ptp.rou2_qte += tp.rou2_qte
        ptp.rou2_cou_scr_can += tp.rou2_cou_scr_can
        ptp.rou2_cou_scr_us += tp.rou2_cou_scr_us

        ptp.rou3_qte += tp.rou3_qte
        ptp.rou3_cou_scr_can += tp.rou3_cou_scr_can
        ptp.rou3_cou_scr_us += tp.rou3_cou_scr_us



        ptp.rou23_qte += tp.rou23_qte
        ptp.rou23_cou_can += tp.rou23_cou_can
        ptp.rou23_cou_us += tp.rou23_cou_us
        ptp.rou23_cou_rep_can += tp.rou23_cou_rep_can
        ptp.rou23_cou_rep_us += tp.rou23_cou_rep_us
        ptp.rou23_cou_ext_can += tp.rou23_cou_ext_can
        ptp.rou23_cou_ext_us += tp.rou23_cou_ext_us

        ptp.scrap_qte += tp.scrap_qte
        ptp.scrap_cou_can += tp.scrap_cou_can
        ptp.scrap_cou_us += tp.scrap_cou_us
        ptp.scrap_cou_rep_can += tp.scrap_cou_rep_can
        ptp.scrap_cou_ext_can += tp.scrap_cou_ext_can
        ptp.scrap_cou_rep_us += tp.scrap_cou_rep_us
        ptp.scrap_cou_ext_us += tp.scrap_cou_ext_us

        ptp.setup_qte += tp.setup_qte
        ptp.setup_cou_scr_can += tp.setup_cou_scr_can
        ptp.setup_cou_scr_us += tp.setup_cou_scr_us

        ptp.vrac_qte += tp.vrac_qte
        ptp.vrac_cou_scr_can += tp.vrac_cou_scr_can
        ptp.vrac_cou_scr_us += tp.vrac_cou_scr_us

    End Sub

    Function GetPexCM(dic As Dictionary(Of Long, pexCommandeMachine), cm As commande_machine, Optional rou As rouleau = Nothing) As pexCommandeMachine
        Dim id_commande = If(rou Is Nothing, cm.id_commande, rou.id_commande)
        'Dim id_machine = If(rou Is Nothing, cm.id_machine, rou.id_machine) ' 41 diff avec PexRpt
        Dim id_machine = cm.id_machine : If id_machine < 1 And rou IsNot Nothing Then id_machine = rou.id_machine
        Dim key = GetDualIdKey(cm.id_commande_machine, id_commande)
        Dim pcm As pexCommandeMachine = Nothing

        'Debug.Assert(cm.id_commande_machine <> 260377)
        'Debug.Assert(cm.id_commande <> 801747)
        'Debug.Assert(cm.id_commande <> 801855)

        If Not dic.TryGetValue(key, pcm) Then

            Dim moiMod = GetMoiModCostsForRun(cm)

            pcm = New pexCommandeMachine With {.CommandeMachineID = cm.id_commande_machine,
                                               .CommandeID = id_commande,
                                               .MachineID = id_machine,
                                               .Machine = If(id_machine > 0, dicMachine(id_machine).nom, ""),
                                               .ReelMachineMontantCan = GetMachineCostForRun(cm),
                                               .REELMoiMontantCan = Math.Round(moiMod.Moi, 2, MidpointRounding.AwayFromZero),
                                               .REELModMontantCan = Math.Round(moiMod.[Mod], 2, MidpointRounding.AwayFromZero),
                                               .BudgetSetupLb = cm.temps_setup_lb,
                                               .DateDebutSetup = NDate(cm.date_debut_setup),
                                               .DateFinSetup = NDate(cm.date_fin_setup),
                                               .DateDebutCommande = NDate(cm.date_debut_commande),
                                               .DateFinCommande = NDate(cm.date_fin_commande),
                                               .OrdreCedule = cm.cedule_order
                                               }
            dic.Add(key, pcm)
        End If
        Return pcm
    End Function


    Private Function GetFxCadPerUsdForRun(cm As commande_machine) As Decimal
        If cm Is Nothing Then Return 1D
        Dim dat? As DateTime = If(cm.date_debut_setup, cm.date_debut_commande)
        If Not dat.HasValue Then dat = AsOfSnapshot.Date

        Dim tc As taux_change = GetTauxChange(dat.Value)  ' uses dicTauxChange with day-by-day fallback
        Dim rate As Decimal = If(tc IsNot Nothing AndAlso tc.taux > 0D, tc.taux, GetStdTaux(dat))

        If rate <= 0D Then rate = 1D
        Return rate           ' CAD per USD
    End Function

    Function SplitTotProd(tot As totauxProduction, ratio As Decimal) As totauxProduction

        If ratio = 1D Then Return tot

        Dim tp = New totauxProduction
        tp.rou01_count = 1 ' le nombre de rouleaux n'est pas au pro-rata de la production...
        tp.rou01_qte = tot.rou01_qte * ratio
        tp.rou01_cou_can = tot.rou01_cou_can * ratio
        tp.rou01_cou_us = tot.rou01_cou_us * ratio
        tp.rou01_cou_std_us = tot.rou01_cou_std_us * ratio
        tp.rou01_cou_lst_us = tot.rou01_cou_lst_us * ratio

        tp.rou2_qte = tot.rou2_qte * ratio
        tp.rou2_cou_scr_can = tot.rou2_cou_scr_can * ratio
        tp.rou2_cou_scr_us = tot.rou2_cou_scr_us * ratio

        tp.rou3_qte = tot.rou3_qte * ratio
        tp.rou3_cou_scr_can = tot.rou3_cou_scr_can * ratio
        tp.rou3_cou_scr_us = tot.rou3_cou_scr_us * ratio




        tp.rou23_qte = tot.rou23_qte * ratio
        tp.rou23_cou_can = tot.rou23_cou_can * ratio
        tp.rou23_cou_us = tot.rou23_cou_us * ratio
        tp.rou23_cou_rep_can = tot.rou23_cou_rep_can * ratio
        tp.rou23_cou_rep_us = tot.rou23_cou_rep_us * ratio
        tp.rou23_cou_ext_can = tot.rou23_cou_ext_can * ratio
        tp.rou23_cou_ext_us = tot.rou23_cou_ext_us * ratio

        tp.scrap_qte = tot.scrap_qte * ratio
        tp.scrap_cou_can = tot.scrap_cou_can * ratio
        tp.scrap_cou_us = tot.scrap_cou_us * ratio
        tp.scrap_cou_rep_can = tot.scrap_cou_rep_can * ratio
        tp.scrap_cou_ext_can = tot.scrap_cou_ext_can * ratio
        tp.scrap_cou_rep_us = tot.scrap_cou_rep_us * ratio
        tp.scrap_cou_ext_us = tot.scrap_cou_ext_us * ratio

        tp.setup_qte = tot.setup_qte * ratio
        tp.setup_cou_scr_can = tot.setup_cou_scr_can * ratio
        tp.setup_cou_scr_us = tot.setup_cou_scr_us * ratio
        tp.setup_budget = tot.setup_budget * ratio ' test

        tp.vrac_qte = tot.vrac_qte * ratio
        tp.vrac_cou_scr_can = tot.vrac_cou_scr_can * ratio
        tp.vrac_cou_scr_us = tot.vrac_cou_scr_us * ratio

        Return tp

    End Function

End Module

