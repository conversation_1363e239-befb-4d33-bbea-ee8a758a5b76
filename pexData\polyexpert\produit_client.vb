﻿Imports ProtoBuf

<PetaPoco.PrimaryKey("ID_PRODUIT_CLIENT")>
<ProtoContract()>
Partial Public Class produit_client
    <ProtoMember(1)> Public Property id_produit_client As Integer
    'Public Property import As Integer?
    <ProtoMember(3)> Public Property id_produit As Integer
    <ProtoMember(4)> Public Property id_client As Integer
    'Public Property tolgram_min As Integer?
    'Public Property tolgram_max As Integer?
    'Public Property epaisseur_min as double?
    'Public Property epaisseur_max as double?
    'Public Property largeur_min as double?
    'Public Property largeur_max as double?
    'Public Property qte_minimum As Integer?
    'Public Property afficher_sur_liste_prix As Boolean
    'Public Property actif As Boolean
    'Public Property id_produit_client_old As Integer?
    'Public Property remplacer_pied_par_msi As Boolean
    'Public Property commentaire As String
End Class