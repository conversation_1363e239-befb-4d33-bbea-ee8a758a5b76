﻿Imports System.IO

Public Class LogFile

    Public ReadOnly Property FileName As String

    Private ReadOnly _lock As New Object

    Sub New(filename As String)
        _FileName = filename
    End Sub

    Sub Log(text As String)
        Dim lines = text.Split({vbCrLf}, StringSplitOptions.None)
        SyncLock _lock
            Using sw = IO.File.AppendText(_FileName)
                For Each line In lines
                    sw.WriteLine($"{Now:yyyy-MM-dd HH:mm:ss} | {line}")
                Next
            End Using
        End SyncLock
    End Sub

    Sub Clear(Optional before As DateTime = #9999-12-31#)
        Dim lines = WriteSafeReadAllLines(FileName)
        Dim kept = New List(Of String)(lines.Count)
        For i = 0 To lines.Count - 1
            If CDate(lines(i).Substring(0, 19)) >= before Then
                kept.Add(lines(i))
            End If
        Next
        SyncLock _lock
            IO.File.WriteAllLines(FileName, kept)
        End SyncLock
    End Sub

    Private Function WriteSafeReadAllLines(ByVal path As String) As String()
        Using fs = New FileStream(path, FileMode.OpenOrCreate, FileAccess.Read, FileShare.ReadWrite)
            Using sr = New StreamReader(fs)
                Dim file As List(Of String) = New List(Of String)()
                While Not sr.EndOfStream
                    file.Add(sr.ReadLine())
                End While
                Return file.ToArray()
            End Using
        End Using
    End Function

End Class

