﻿Imports ProtoBuf
Imports PetaPoco

<PetaPoco.PrimaryKey("RnDResourcesSpendingID", autoIncrement:=True)>
<PetaPoco.TableName("RnDResourcesSpending")>
<ProtoContract>
Partial Public Class pexRnDResourcesSpending
    Implements ISortID

    <ProtoMember(1)>
    Public Property UniqueID As Long Implements ISortID.UniqueID
    <ProtoMember(2)>
    Public Property WorkDate As DateTime?
    <ProtoMember(3)>
    Public Property Annee As Integer?
    <ProtoMember(4)>
    Public Property AnneeFiscale As Integer?
    <ProtoMember(5)>
    Public Property Mois As Integer?
    <ProtoMember(6)>
    Public Property Jour As Integer?
    <ProtoMember(7)>
    Public Property EmployeID As Integer Implements ISortID.SortID, ISortID.SortID2
    <ProtoMember(8)>
    Public Property EmployeeName As String
    <ProtoMember(9)>
    Public Property RnDProjectName As String
    <ProtoMember(10)>
    Public Property RnDProjectType As String
    <ProtoMember(11)>
    Public Property RnDProjectDescription As String
    <ProtoMember(12)>
    Public Property WorkTimeMin As Integer
    <ProtoMember(13)>
    Public Property WorkTimeMinAdjusted As Integer?

    ' Map the old names to the new CAD columns
    <ProtoMember(14), Column("LabourCostCan")>
    Public Property LabourCost As Decimal?
    <ProtoMember(15), Column("LabourCostAdjustedCan")>
    Public Property LabourCostAdjusted As Decimal?

    ' New US columns (append so protobuf numbering stays stable)
    <ProtoMember(16), Column("LabourCostUs")>
    Public Property LabourCostUs As Decimal?
    <ProtoMember(17), Column("LabourCostAdjustedUs")>
    Public Property LabourCostAdjustedUs As Decimal?
End Class
