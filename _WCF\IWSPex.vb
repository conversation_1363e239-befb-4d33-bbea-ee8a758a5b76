﻿Imports System.IO
Imports System.ServiceModel
Imports System.ServiceModel.Web

<ServiceContract()>
Public Interface IWSPex

    <WebInvoke(Method:="GET", UriTemplate:="Version")>
    Function Version() As Stream

    <WebInvoke(Method:="GET", UriTemplate:="GetListeCheck")>
    Function GetListeCheck() As Stream

    <WebInvoke(Method:="GET", UriTemplate:="GetListePexCommande")>
    Function GetListePexCommande() As Stream

    <WebInvoke(Method:="GET", UriTemplate:="GetListePexFacture")>
    Function GetListePexFacture() As Stream

    ' test: testws "http://TI07:8184/WSPex/StartPolyExpertReport"
    <WebInvoke(Method:="GET", UriTemplate:="StartPolyExpertReport?ResponseEmail={ResponseEmail}")>
    Function StartPolyExpertReport(Optional ResponseEmail As String = "") As Stream

    <WebInvoke(Method:="GET", UriTemplate:="CalculCoutTransport")>
    Function CalculCoutTransport() As Stream

    <WebInvoke(Method:="GET", UriTemplate:="CalculCoutPVActuel")>
    Function CalculCoutPVActuel() As Stream

    <WebInvoke(Method:="GET", UriTemplate:="CalculSTDPVCoutActualisable?id_pv={id_pv}")>
    Function CalculSTDPVCoutActualisable(Optional id_pv As Integer = 0) As Stream ' valeur autre que 0 ne fonctionne pas

    <WebInvoke(Method:="GET", UriTemplate:="CalculCoutStructure")>
    Function CalculCoutStructure() As Stream

End Interface

