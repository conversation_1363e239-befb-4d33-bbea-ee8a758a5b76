﻿Imports ProtoBuf

<PetaPoco.PrimaryKey("ID_MACHINE")>
<ProtoContract()>
Partial Public Class machine
    <ProtoMember(1)> Public Property id_machine As Integer
    <ProtoMember(3)> Public Property nom As String
    <ProtoMember(8)> Public Property nb_vis As Integer
    <ProtoMember(26)> Public Property actif As Boolean
    <ProtoMember(27)> Public Property reel_machine_can_montant_heure As Decimal
End Class