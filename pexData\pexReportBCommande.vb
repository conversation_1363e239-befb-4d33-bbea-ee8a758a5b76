﻿Imports Utilities
Imports UtilitiesData
Imports DualID = System.Int64

Public Module pexReportBCommande

    Public lstPexCmd As List(Of pexCommande)
    Public dicPexCmd As Dictionary(Of Integer, pexCommande)
    Private dicPexCmdPond As Dictionary(Of DualID, Decimal) ' dual id is ProduitClientID, Period (yyyymm)
    Private dicCMGroup As Dictionary(Of Integer, List(Of pexCommandeMachine))

    Public Sub CleanMemory()
        lstPexCmd.Clear()
        dicPexCmd.Clear()
        dicPexCmdPond.Clear()
        dicCMGroup.Clear()
    End Sub

    Public Sub BuildCommande()

        Using New Chrono(Sub(ms) Log($"BuildCommande done. Elapsed: {ms:#,0}ms"), Sub() Log("BuildCommande started...", color:=ConsoleColor.White))

            lstPexCmd = New List(Of pexCommande)
            dicPexCmd = New Dictionary(Of Integer, pexCommande)
            dicPexCmdPond = New Dictionary(Of DualID, Decimal)

            CreateGroupCM()

            CommandeStep1Header()
            CommandeStep2CommandeMachine()
            CommandeStep3Facture()
            CommandeStep4Standard()
            CommandeStep5PostProcessing()

            CommandeStep6RequisitionFacture() ' partie faite dans spRemplirRequisitionFacture, mais peut être faite ici...

        End Using

    End Sub

    Private Sub CommandeStep1Header()

        For Each cmd As commande In lstCommande

            Dim pc = GetPexCommande(cmd.id_commande)

            Dim cli = TryGetValue(dicClient, cmd.id_client)
            Dim cla = TryGetValue(dicClasse, cli.id_classe)
            Dim pv = TryGetValue(dicPV, cmd.id_pv)
            Dim prd = TryGetValue(dicProduit, pv.id_produit)
            Dim mar = TryGetValue(dicMarche, prd.id_marche)
            Dim cat = TryGetValue(dicProduitCateg, prd.id_produit_categ)
            Dim prdCli = TryGetValue(dicProduitClient, GetDualIdKey(pv.id_produit, pv.id_client))
            Dim ter = TryGetValue(dicTerritoire, cli.id_territoire)
            Dim ach = TryGetValue(dicAdresseClientHistorique, GetDualIdKey(cmd.id_client, cmd.id_adresse)).Where(Function(x) cmd.date_commande >= x.date_debut And cmd.date_commande < x.date_fin).FirstOrDefault
            If ach Is Nothing Then Debug.Write("")
            Dim sec = TryGetValue(dicSecteur, If(ach Is Nothing, cmd.id_secteur, ach.id_secteur)) ' fall back sur commande pour les rares cas qui sont null
            If sec Is Nothing Then Debug.Write("")

            pc.CommandeID = cmd.id_commande
            pc.Commande = cmd.no_affichage
            pc.CommandeVendeurID = cmd.id_vendeur
            pc.CommandeVendeur = TryGetValue(dicVendeur, cmd.id_vendeur).nom
            pc.ClientID = cmd.id_client
            pc.Client = cli.nom
            pc.ClientTri = cli.nom_tri
            pc.ClientTypeID = cla.id_classe
            pc.ClientType = cla.nom
            pc.ClientVendeurID = cli.id_vendeur
            pc.ClientVendeur = TryGetValue(dicVendeur, cli.id_vendeur).nom
            pc.ProduitID = prd.id_produit
            pc.Produit = prd.nom
            pc.ProduitRegroupement = prd.regroupement
            pc.ProduitCategorie = cat.nom_categ
            pc.ProduitTypeID = mar.id_marche
            pc.ProduitType = mar.nom
            pc.ProduitClientID = prdCli.id_produit_client
            pc.PvID = cmd.id_pv
            pc.Pv = pv.chaine_pv
            pc.MarcheStrategique = prd.marche_strategique
            pc.Marche = prd.marche
            pc.SousMarche = prd.sous_marche
            pc.SousMarcheSecondaire = prd.sous_marche_secondaire
            pc.TerritoireID = ter.id_territoire
            pc.Territoire = ter.nom

            If pc.MachineID = 0 AndAlso cmd.id_machine > 0 Then
                pc.MachineID = cmd.id_machine
                pc.Machine = dicMachine(cmd.id_machine).nom
            End If
            pc.CommandeDate = cmd.date_commande
            pc.CommandeDateAnneeFiscale = AnneeFiscale(cmd.date_commande)
            pc.CommandeDateMoisFiscale = MoisFiscal(cmd.date_commande)
            pc.CommandeDateSemaineFiscale = SemaineFiscale(cmd.date_commande)
            pc.CommandeDateJourFiscale = JourFiscal(cmd.date_commande)
            pc.CommandeLargeur = GetLargeurFractionnaire(CDec(cmd.largeur_1), CDec(cmd.largeur_2), CDec(cmd.largeur_3))
            pc.CommandeEpaisseur = CDec(cmd.epaisseur)
            pc.CommandeFormeID = cmd.id_forme
            pc.CommandeForme = TryGetValue(dicForme, cmd.id_forme).nom
            pc.CommandeTraitement = cmd.type_traitement
            pc.CommandeLbs = cmd.poids

            pc.ListePrixLbsChoisi = cmd.liste_prix_qte

            'If cmd.escompte_liste_prix > 0 Then
            pc.CommandeEscompteTarif = cmd.escompte_liste_prix
            'Else
            'pc.CommandeEscompteTarif = 0
            'End If
            pc.CommandeMontantLbs = If(cmd.prix_lbs = 0, 0, CDec(cmd.prix_lbs + cmd.surcharge))
            pc.CommandeMontant = CDec(cmd.poids * (cmd.prix_lbs + cmd.surcharge))
            pc.CommandeDeviseID = cmd.id_devise
            pc.CommandeDevise = TryGetValue(dicDevise, cmd.id_devise).nom
            pc.CommandeAdresseID = cmd.id_adresse
            'pc.CommandeSecteurID = cmd.id_secteur
            'pc.CommandeSecteur = TryGetValue(dicSecteur, cmd.id_secteur).nom
            pc.CommandeSecteurID = sec.id_secteur
            pc.CommandeSecteur = sec.nom
            pc.CommandeEchantillon = CBool(cmd.type = "Échantillon (S)")
            pc.CommandeStatutID = cmd.id_statut
            pc.CommandeStatut = TryGetValue(dicStatut, cmd.id_statut).nom
            pc.CommandeStatutFacturationID = cmd.id_statut_fact
            pc.CommandeStatutFacturation = TryGetValue(dicStatutFact, cmd.id_statut_fact).nom
            pc.CommandeStatutExpeditionID = cmd.id_statut_exped
            pc.CommandeStatutExpedition = TryGetValue(dicStatutExped, cmd.id_statut_exped).nom
            pc.CommandeNbRouleau = cmd.nb_rouleau_cal
        Next

    End Sub

    Private Sub CommandeStep2CommandeMachine()

        For Each gcm In ccdCM.Values

            'Dim key = 0L
            Dim pc As pexCommande = Nothing
            Dim cm = gcm.cmdMac

            If gcm.totProd Is Nothing Then
                ' process rouleau expediables
                For Each gr In gcm.rouleaux
                    If gr.rou.totProd IsNot Nothing Then
                        Dim r = gr.rou
                        pc = GetPexCommande(r.id_commande)
                        AddTotProdToPexCommande(pc, gr.rou.totProd)
                    End If
                Next
            Else
                ' pas de production expediable, on crée un record pour la scrap			
                pc = GetPexCommande(cm.id_commande)
                AddTotProdToPexCommande(pc, gcm.totProd)
            End If

            pc.OrdreCedule = cm.cedule_order

            If pc.CommandeStatutID = 7 And pc.CommandeStatutFacturationID = 1 And pc.CommandeStatutExpeditionID = 1 Then
                Dim lstPCM As List(Of pexCommandeMachine) = TryGetValue(dicCMGroup, pc.CommandeID)
                If lstPCM IsNot Nothing Then
                    pc.REELResineSTDUsMontantLbs = lstPCM.Sum(Function(x) x.REELResineSTDUsMontantLbs)
                    pc.REELResineListeUsMontantLbs = lstPCM.Sum(Function(x) x.REELResineListeUsMontantLbs)
                End If
            End If

        Next

        For Each pc In lstPexCmd

            ' do totaux/calc on pcm		
            'pc.Machine = If(pc.MachineID > 0, dicMachine(pc.MachineID).nom, Nothing)

            Dim tp = pc.totProd

            If tp IsNot Nothing Then

                pc.REELResineCanMontantLbs = Div0(tp.rou01_cou_can, tp.rou01_qte)
                pc.REELResineUsMontantLbs = Div0(tp.rou01_cou_us, tp.rou01_qte)
                pc.REELResineTauxUs = Div0(pc.REELResineCanMontantLbs, pc.REELResineUsMontantLbs)


                pc.REELResineScrapCanMontantLbs = Div0((tp.rou23_cou_can + tp.scrap_cou_can), (tp.rou23_qte + tp.scrap_qte))
                pc.REELResineScrapUsMontantLbs = Div0((tp.rou23_cou_us + tp.scrap_cou_us), (tp.rou23_qte + tp.scrap_qte))

                pc.REELResineScrapTauxUs = Div0(pc.REELResineScrapCanMontantLbs, pc.REELResineScrapUsMontantLbs)

                pc.REELProductionExpediable = tp.rou01_qte
                pc.REELNbRouleauExpediable = tp.rou01_count

                pc.REELRejetSetup = tp.setup_qte
                pc.REELRejetSetupCan = tp.setup_cou_scr_can
                pc.REELRejetSetupUs = tp.setup_cou_scr_us

                pc.REELRejetVrac = tp.vrac_qte
                pc.REELRejetVracCan = tp.vrac_cou_scr_can
                pc.REELRejetVracUs = tp.vrac_cou_scr_us

                pc.REELRejetNCR = tp.rou3_qte
                pc.REELRejetNCRCan = tp.rou3_cou_scr_can
                pc.REELRejetNCRUs = tp.rou3_cou_scr_us

                pc.REELRejetNCH = tp.rou2_qte
                pc.REELRejetNCHCan = tp.rou2_cou_scr_can
                pc.REELRejetNCHUs = tp.rou2_cou_scr_us

                pc.REELRejetTotalExtrusionCan = tp.rou23_cou_ext_can + tp.scrap_cou_ext_can
                pc.REELRejetTotalExtrusionUs = tp.rou23_cou_ext_us + tp.scrap_cou_ext_us
                pc.REELRejetTotalReprocessCan = tp.rou23_cou_rep_can + tp.scrap_cou_rep_can '-gg-
                pc.REELRejetTotalReprocessUs = tp.rou23_cou_rep_us + tp.scrap_cou_rep_us

                ' Champs dérivés
                pc.REELRejetTotal = pc.REELRejetSetup + pc.REELRejetVrac + pc.REELRejetNCH + pc.REELRejetNCR

                pc.REELRejetTotalCan = pc.REELRejetSetupCan + pc.REELRejetVracCan + pc.REELRejetNCHCan + pc.REELRejetNCRCan

                pc.REELRejetTotalUs = pc.REELRejetSetupUs + pc.REELRejetVracUs + pc.REELRejetNCHUs + pc.REELRejetNCRUs

                pc.REELProductionBrute = pc.REELProductionExpediable + pc.REELRejetTotal
                pc.REELRejetPourcentage = Div0(pc.REELRejetTotal, pc.REELProductionBrute)

                ' Description du calcul: NOTE CE CODE EST A DEUX ENDROITS
                ' A) on prend le cout de la scrap basé sur la structure (cout réel de la scrap selon les résines du mélange)
                ' B) on ajoute le cout du reprocess et de l'extrusion (couts fixes selon std_variable_globale)
                ' C) on soustrait le cout de la résine scrap (la valeur que l'on attribue a ce qui reste après le reprocess)
                ' D) on obtient ainsi combien nous a couté la scrap produit (cout initial selon qte*résine + processus - valeur après récupération)
                Dim tmp1 = (pc.REELRejetTotal * pc.REELResineScrapCanMontantLbs) _
                          + pc.REELRejetTotalReprocessCan _
                          + pc.REELRejetTotalExtrusionCan _
                          - pc.REELRejetTotalCan

                pc.REELScrapCanMontantLbs = Div0(tmp1, pc.REELProductionExpediable)

                'Debug.Assert(pc.CommandeID <> 801903)

                Dim tmp2 = (pc.REELRejetTotal * pc.REELResineScrapUsMontantLbs) _
                          + pc.REELRejetTotalReprocessUs _
                          + pc.REELRejetTotalExtrusionUs _
                          - pc.REELRejetTotalUs

                pc.REELScrapUsMontantLbs = Div0(tmp2, pc.REELProductionExpediable)

            End If

        Next

    End Sub

    Private Sub CommandeStep3Facture()

        '-TODO- possible extract to facture processing
        For Each item In lstFactureItem
            If Not PRD_EXCLUS_POUR_CALCUL_POIDS.Contains("" & item.code_produit) Then
                Dim fac = GetFacture(item.id_facture)
                Dim cmd = GetCommande(fac.id_commande)
                If cmd IsNot Nothing Then
                    'If cmd.id_commande = 793555 Then Debug.Write("")
                    fac.calc_FactureLbs += GetPoidsLb(item.unite, CDec(item.quantite_expedie), cmd.longueur, cmd.poids, CDec(cmd.poids_rouleau_cal), item.code_produit)
                End If
            End If
        Next

        For Each fac In lstFacture
            If fac.id_commande > 0 Then
                Dim pc = GetPexCommande(fac.id_commande)
                If pc.CommandeStatutFacturationID = 1 Then
                    If fac.date_facturation > pc.STDFACDate Then pc.STDFACDate = fac.date_facturation
                End If
                pc.FactureLbs += fac.calc_FactureLbs
            End If
        Next

    End Sub

    Private Sub CommandeStep4Standard()

        Dim useNew = INI.ReadKey("Config", "UseOldCoutRejet", "0") = "0"

        If useNew Then InitPEXRejet(ppdb) ' TODO: centralise?        

        Dim cNew, cOld As Integer
        Dim dNew, dOld, dWas As Decimal

        For Each cmd In lstCommande

            Dim pc = GetPexCommande(cmd.id_commande)

            Dim spc = TryGetValue(dicStdProduitClient, (pc.ProduitClientID, cmd.date_commande.Year, cmd.date_commande.Month))
            Dim spr = TryGetValue(dicStdProduit, (pc.ProduitClientID, cmd.date_commande.Year, cmd.date_commande.Month))
            Dim cmdCTA = GetStdCoutTransportAdresse(pc.CommandeAdresseID, pc.CommandeDate)
            Dim cmdCTS = GetStdCoutTransportSecteur(cmdCTA.id_secteur_transport, pc.CommandeDate)

            'Debug.Assert(spc IsNot Nothing)
            'Debug.Assert(spr IsNot Nothing)
            pc.STDProduitClientVolumeObjectif = spc.obj_volume
            pc.STDProduitClientVolumeObjectifRevise = spc.obj_volume_revise
            pc.STDProduitClientVolumeObjectifReforecast = spc.obj_volume_reforecast
            pc.STDProduitClientVolumeObjectifReviseReforecast = -spc.obj_volume_revise_reforecast

            pc.STDProduitCmObjCanMontantLbs = spr.obj_cm_can
            pc.STDProduitCmObjReviseCanMontantLbs = spr.obj_cm_revise_can
            pc.STDProduitCmObjUsMontantLbs = spr.obj_cm_us
            pc.STDProduitCmObjReviseUsMontantLbs = spr.obj_cm_revise_us

            pc.STDProduitClientEscompteObjectifCanMontantLbs = spc.obj_escompte_can
            pc.STDProduitClientEscompteObjectifReviseCanMontantLbs = spc.obj_escompte_revise_can
            pc.STDProduitClientEscompteObjectifUsMontantLbs = spc.obj_escompte_us
            pc.STDProduitClientEscompteObjectifReviseUsMontantLbs = spc.obj_escompte_revise_us

            pc.CommandeRetD = cmd.commande_retd

            If cmdCTA.id_cout_transport_source_calcul = 1 Then '-todo- lire la table std_cout_transport_type_calcul et utiliser la description qui provient de la table
                pc.STDVCTransportSecteurvsHistorique = "Secteur"
            ElseIf cmdCTA.id_cout_transport_source_calcul = 2 Then
                pc.STDVCTransportSecteurvsHistorique = "Historique (12 mois)"
            Else
                pc.STDVCTransportSecteurvsHistorique = ""
            End If
            'If Not cmdCT.client_paie_transport Or cmdCT.prix_vente_inclut_transport Then
            If cmdCTA.client_paie_transport = True Or cmdCTA.prix_vente_inclut_transport = False Then
                pc.STDVCTransportInclus = False
            Else
                pc.STDVCTransportInclus = True
            End If
            pc.STDVCTransportSecteurCanMontantLbs = If(cmdCTS.cout_transport_standard_can, 0) 'cou_std ok
            pc.STDVCTransportSecteurUsMontantLbs = If(cmdCTS.cout_transport_standard_us, 0)
            If Not cmdCTA.cout_transport_standard_can.HasValue Then
                pc.STDVCTransportHistoriqueCanMontantLbs = 0
                pc.STDVCTransportHistoriqueUsMontantLbs = 0
                pc.STDVCTransportHistoriqueCanMontant = 0
                pc.STDVCTransportHistoriqueUsMontant = 0
            Else
                pc.STDVCTransportHistoriqueCanMontantLbs = cmdCTA.cout_transport_standard_can.Value 'cou_std ok
                pc.STDVCTransportHistoriqueUsMontantLbs = cmdCTA.cout_transport_standard_us.Value
                pc.STDVCTransportHistoriqueCanMontant = pc.STDVCTransportHistoriqueCanMontantLbs * pc.CommandeLbs
                pc.STDVCTransportHistoriqueUsMontant = pc.STDVCTransportHistoriqueUsMontantLbs * pc.CommandeLbs
            End If
            pc.STDVCTransportCanMontantLbs = If(cmdCTA.cout_transport_standard_can, 0) 'cou_std ok 
            pc.STDVCTransportUsMontantLbs = If(cmdCTA.cout_transport_standard_us, 0)
            pc.STDVCDouaneCanMontantLbs = If(cmdCTA.douane_assurance_can, 0)
            pc.STDVCDouaneUsMontantLbs = If(cmdCTA.douane_assurance_us, 0)
            pc.STDVCEntreposageCanMontantLbs = If(cmdCTA.entreposage_can, 0)
            pc.STDVCEntreposageUsMontantLbs = If(cmdCTA.entreposage_us, 0)
            pc.STDVCRabaisClientCanMontantLbs = If(cmdCTA.rabais_client_can, 0)
            pc.STDVCRabaisClientUsMontantLbs = If(cmdCTA.rabais_client_us, 0)
            pc.STDVCEntrepotExterneCanMontantLbs = If(cmdCTA.entrepot_externe_can, 0)
            pc.STDVCEntrepotExterneUsMontantLbs = If(cmdCTA.entrepot_externe_us, 0)

            pc.STDVCTransportCanMontant = pc.STDVCTransportCanMontantLbs * pc.CommandeLbs
            pc.STDVCTransportUsMontant = pc.STDVCTransportUsMontantLbs * pc.CommandeLbs
            pc.STDVCTransportSecteurCanMontant = pc.STDVCTransportSecteurCanMontantLbs * pc.CommandeLbs
            pc.STDVCTransportSecteurUsMontant = pc.STDVCTransportSecteurUsMontantLbs * pc.CommandeLbs

            pc.STDVCDouaneCanMontant = pc.STDVCDouaneCanMontantLbs * pc.CommandeLbs
            pc.STDVCDouaneUsMontant = pc.STDVCDouaneUsMontantLbs * pc.CommandeLbs
            pc.STDVCEntreposageCanMontant = pc.STDVCEntreposageCanMontantLbs * pc.CommandeLbs
            pc.STDVCEntreposageUsMontant = pc.STDVCEntreposageUsMontantLbs * pc.CommandeLbs
            pc.STDVCRabaisClientCanMontant = pc.STDVCRabaisClientCanMontantLbs * pc.CommandeLbs
            pc.STDVCRabaisClientUsMontant = pc.STDVCRabaisClientUsMontantLbs * pc.CommandeLbs
            pc.STDVCEntrepotExterneCanMontant = pc.STDVCEntrepotExterneCanMontantLbs * pc.CommandeLbs
            pc.STDVCEntrepotExterneUsMontant = pc.STDVCEntrepotExterneUsMontantLbs * pc.CommandeLbs

            Dim stdPvCout As std_pv_cout
            Dim stdCatSur As std_categorie_surcharge = Nothing
            Dim stdCoutRejet As CoutRejetCalcul = Nothing

            Dim stdTaux As Decimal
            Dim cliHis As client_historique
            Dim vg As std_variable_globale

            ' Données COMMANDE

            If pc.CommandeDate > Nothing Then ' nothing is 0001-01-01

                Dim dat = pc.CommandeDate
                stdPvCout = GetStdPvCout(pc.Pv, dat, pc.PvID)
                If useNew AndAlso dat >= DEBUT_NOUVEAU_CALCUL Then
                    stdCoutRejet = GetCoutRejet(cmd.id_pv, cmd.date_commande, cmd.poids, cmd.id_client, cmd.id_commande)
                    If stdCoutRejet Is Nothing Then Debug.Write("")
                End If

                stdCatSur = GetStdCatSurcharge(stdPvCout.id_std_categorie, CInt(pc.CommandeLbs), dat)
                'stdCatSur = GetStdCatSurcharge(stdPvCout.id_std_categorie, If(pc.ListePrixLbsChoisi <> 0, pc.ListePrixLbsChoisi, CInt(pc.CommandeLbs)), dat)

                stdTaux = GetStdTaux(dat)
                cliHis = GetClientHistorique(pc.ClientID, dat)
                vg = GetVarGlob(dat)

                pc.STDCMDCategorieID = stdPvCout.id_std_categorie
                If useNew AndAlso dat >= #2022-09-01# AndAlso stdCoutRejet IsNot Nothing Then
                    pc.STDCMDCategorieSurcharge = stdCoutRejet.nom_cat_surcharge
                    'cNew += 1
                Else
                    pc.STDCMDCategorieSurcharge = stdCatSur.nom
                    'If dat >= #2022-09-01# Then cOld += 1
                End If

                If useNew AndAlso dat >= #2022-09-01# AndAlso stdCoutRejet IsNot Nothing Then
                    pc.STDCMDCategorieSurchargePourcentageScrap = stdCoutRejet.pourcentage_scrap
                    'cNew += 1
                Else
                    pc.STDCMDCategorieSurchargePourcentageScrap = stdCatSur.pourcentage_scrap_1_couche
                    'If dat >= #2022-09-01# Then cOld += 1
                End If
                pc.STDCMDTauxUS = stdTaux

                Dim TauxPourCalculDevise_can As Decimal = If(pc.CommandeDeviseID = 3, 1D, stdTaux)

                pc.STDCMDPrixVenteCanBrutMontantLbs = CalculDevise_Can(pc.CommandeMontantLbs, TauxPourCalculDevise_can)
                'If pc.CommandeID = 805618 Then
                '    Debug.WriteLine("")
                'End If

                pc.STDCMDCommissionVendeurCanMontantLbs = Calculer_Vendeur_Taux_Commission(pc.ClientVendeurID, dat, pc.STDCMDPrixVenteCanBrutMontantLbs, AddressOf CalculDevise_Can, TauxPourCalculDevise_can, Devise_can)
                pc.STDCMDRedevanceClientCanMontantLbs = Calculer_Client_Redevance(pc.ClientID, dat, pc.STDCMDPrixVenteCanBrutMontantLbs, AddressOf CalculDevise_Can, TauxPourCalculDevise_can, Devise_can)

                pc.STDCMDEscompteCanMontantLbs = CalculDevise_Can(pc.CommandeEscompteTarif, TauxPourCalculDevise_can)


                's il existe un escompte, on calcul le prix initial avec le prix brut +escompte pour revenir au prix initial
                pc.STDCMDPrixVenteInitialCanBrutMontantLbs = pc.STDCMDPrixVenteCanBrutMontantLbs + pc.STDCMDEscompteCanMontantLbs

                pc.STDCMDRabaisVolumeCanMontantLbs = CalculDevise_Can(cliHis.rabais_volume_lbs, TauxPourCalculDevise_can)

                pc.STDCMDResineCanMontantLbs = stdPvCout.cout * stdTaux
                If useNew AndAlso dat >= #2022-09-01# AndAlso stdCoutRejet IsNot Nothing Then
                    'Debug.Assert(stdCoutRejet.cout_rejet_std_US_lb >= 0)
                    Debug.Assert(stdTaux >= 0)
                    pc.STDCMDScrapCanMontantLbs = stdCoutRejet.cout_rejet_std_US_lb * stdTaux
                    'cNew += 1
                Else
                    Debug.Assert(stdCatSur.surcharge >= 0)
                    Debug.Assert(stdTaux >= 0)
                    pc.STDCMDScrapCanMontantLbs = stdCatSur.surcharge * stdTaux '-gg-rejet-... stdCatSur.Surcharge - remplacer par fonction
                    'If dat >= #2022-09-01# Then cOld += 1
                End If
                Dim TauxPourCalculDevise_US As Decimal = If(pc.CommandeDeviseID = 4, 1D, stdTaux)

                pc.STDCMDVariableGlobaleCanMontantLbs = (vg.total + cliHis.cout_emballage)
                pc.STDCMDRabaisClientCanMontantLbs = CalculDevise_Can(If(cliHis.rabais_actif AndAlso cliHis.rabais_type = 2, cliHis.rabais_montant, 0), TauxPourCalculDevise_can)

                pc.STDCMDPrixVenteUsBrutMontantLbs = CalculDevise_US(pc.CommandeMontantLbs, TauxPourCalculDevise_US)

                pc.STDCMDCommissionVendeurUsMontantLbs = Calculer_Vendeur_Taux_Commission(pc.ClientVendeurID, dat, pc.STDCMDPrixVenteUsBrutMontantLbs, AddressOf CalculDevise_US, TauxPourCalculDevise_US, devise_us)
                pc.STDCMDRedevanceClientUsMontantLbs = Calculer_Client_Redevance(pc.ClientID, dat, pc.STDCMDPrixVenteUsBrutMontantLbs, AddressOf CalculDevise_US, TauxPourCalculDevise_US, devise_us)

                pc.STDCMDEscompteUsMontantLbs = CalculDevise_US(pc.CommandeEscompteTarif, TauxPourCalculDevise_US)
                If pc.CommandeID = 805619 Then
                    'pc.STDCMDEscompteUsMontantLbs = 0.1D
                End If
                's il existe un escompte, on calcul le prix initial avec le prix brut +escompte pour revenir au prix initial
                pc.STDCMDPrixVenteInitialUsBrutMontantLbs = pc.STDCMDPrixVenteUsBrutMontantLbs + pc.STDCMDEscompteUsMontantLbs

                pc.STDCMDRabaisVolumeUsMontantLbs = CalculDevise_US(cliHis.rabais_volume_lbs, TauxPourCalculDevise_US)
                pc.STDCMDResineUsMontantLbs = stdPvCout.cout

                If useNew AndAlso dat >= #2022-09-01# AndAlso stdCoutRejet IsNot Nothing Then
                    'Debug.Assert(stdCoutRejet.cout_rejet_std_US_lb >= 0)
                    pc.STDCMDScrapUsMontantLbs = stdCoutRejet.cout_rejet_std_US_lb
                    cNew += 1
                    dNew += stdCoutRejet.cout_rejet_std_US_lb
                    dWas += stdCatSur.surcharge
                Else
                    Debug.Assert(stdCatSur.surcharge >= 0)
                    pc.STDCMDScrapUsMontantLbs = stdCatSur.surcharge
                    If dat >= #2022-09-01# Then
                        cOld += 1
                        dOld += stdCatSur.surcharge
                    End If
                End If
                pc.STDCMDVariableGlobaleUsMontantLbs = (vg.total + cliHis.cout_emballage) / stdTaux
                pc.STDCMDRabaisClientUsMontantLbs = CalculDevise_US(If(cliHis.rabais_actif AndAlso cliHis.rabais_type = 2, cliHis.rabais_montant, 0), TauxPourCalculDevise_US)
            End If

            ' Données PRODUCTION

            Dim lst As List(Of commande_machine) = Nothing
            If dicCmdMac_Cmd.TryGetValue(pc.CommandeID, lst) Then
                If lst.First.date_fin_commande.HasValue Then
                    pc.STDPRDDate = lst.First.date_fin_commande.Value
                End If
            End If

            '* S'il y a une date dedans, c'est la seule façon de vérifier.
            If pc.STDPRDDate > CDate("1900-01-01") Then
                Dim dat = pc.STDPRDDate
                stdPvCout = GetStdPvCout(pc.Pv, dat, pc.PvID)
                If useNew AndAlso dat >= DEBUT_NOUVEAU_CALCUL Then
                    stdCoutRejet = GetCoutRejet(cmd.id_pv, cmd.date_commande, cmd.poids, cmd.id_client, cmd.id_commande, dat.Value)
                    If stdCoutRejet Is Nothing Then Debug.Write("")
                Else
                    stdCatSur = GetStdCatSurcharge(stdPvCout.id_std_categorie, CInt(pc.CommandeLbs), dat)
                    'stdCatSur = GetStdCatSurcharge(stdPvCout.id_std_categorie, If(pc.ListePrixLbsChoisi <> 0, pc.ListePrixLbsChoisi, CInt(pc.CommandeLbs)), dat)

                    If dat >= #2022-09-01# Then cOld += 1
                End If
                stdTaux = GetStdTaux(dat)
                cliHis = GetClientHistorique(pc.ClientID, dat)
                vg = GetVarGlob(dat)

                pc.STDPRDCategorieID = stdPvCout.id_std_categorie
                If useNew AndAlso dat >= #2022-09-01# AndAlso stdCoutRejet IsNot Nothing Then
                    pc.STDPRDCategorieSurcharge = stdCoutRejet.id_std_categorie_surcharge
                    'cNew += 1
                Else
                    pc.STDPRDCategorieSurcharge = stdCatSur.nom
                    'If dat >= #2022-09-01# Then cOld += 1
                End If
                pc.STDPRDDateAnneeFiscale = AnneeFiscale(dat)
                pc.STDPRDDateMoisFiscale = MoisFiscal(dat)
                pc.STDPRDDateSemaineFiscale = SemaineFiscale(dat)
                pc.STDPRDDateJourFiscale = JourFiscal(dat)
                pc.STDPRDTauxUS = stdTaux

                Dim TauxPourCalculDevise_can As Decimal = If(pc.CommandeDeviseID = 3, 1D, stdTaux)

                pc.STDPRDPrixVenteCanBrutMontantLbs = CalculDevise_Can(pc.CommandeMontantLbs, TauxPourCalculDevise_can)

                pc.STDPRDCommissionVendeurCanMontantLbs = Calculer_Vendeur_Taux_Commission(pc.ClientVendeurID, dat, pc.STDPRDPrixVenteCanBrutMontantLbs, AddressOf CalculDevise_Can, TauxPourCalculDevise_can, Devise_can)
                pc.STDPRDRedevanceClientCanMontantLbs = Calculer_Client_Redevance(pc.ClientID, dat, pc.STDPRDPrixVenteCanBrutMontantLbs, AddressOf CalculDevise_Can, TauxPourCalculDevise_can, Devise_can)

                pc.STDPRDEscompteCanMontantLbs = CalculDevise_Can(pc.CommandeEscompteTarif, TauxPourCalculDevise_can)
                If pc.CommandeID = 805619 Then
                    'pc.STDCMDEscompteUsMontantLbs = 0.1D
                End If
                's il existe un escompte, on calcul le prix initial avec le prix brut +escompte pour revenir au prix initial

                pc.STDPRDPrixVenteInitialCanBrutMontantLbs = pc.STDPRDPrixVenteCanBrutMontantLbs + pc.STDPRDEscompteCanMontantLbs

                pc.STDPRDRabaisVolumeCanMontantLbs = CalculDevise_Can(cliHis.rabais_volume_lbs, TauxPourCalculDevise_can)
                pc.STDPRDResineCanMontantLbs = stdPvCout.cout * stdTaux
                If useNew AndAlso dat >= #2022-09-01# AndAlso stdCoutRejet IsNot Nothing Then
                    pc.STDPRDScrapCanMontantLbs = stdCoutRejet.cout_rejet_std_US_lb * stdTaux
                    'cNew += 1
                Else
                    pc.STDPRDScrapCanMontantLbs = stdCatSur.surcharge * stdTaux
                    'If dat >= #2022-09-01# Then cOld += 1
                End If
                Dim TauxPourCalculDevise_us As Decimal = If(pc.CommandeDeviseID = 4, 1D, stdTaux)

                pc.STDPRDVariableGlobaleCanMontantLbs = (vg.total + cliHis.cout_emballage)
                pc.STDPRDRabaisClientCanMontantLbs = CalculDevise_Can(If(cliHis.rabais_actif AndAlso cliHis.rabais_type = 2, cliHis.rabais_montant, 0), TauxPourCalculDevise_can)

                pc.STDPRDPrixVenteUsBrutMontantLbs = CalculDevise_US(pc.CommandeMontantLbs, TauxPourCalculDevise_us)

                pc.STDPRDCommissionVendeurUsMontantLbs = Calculer_Vendeur_Taux_Commission(pc.ClientVendeurID, dat, pc.STDPRDPrixVenteUsBrutMontantLbs, AddressOf CalculDevise_US, TauxPourCalculDevise_us, devise_us)
                pc.STDPRDRedevanceClientUsMontantLbs = Calculer_Client_Redevance(pc.ClientID, dat, pc.STDPRDPrixVenteUsBrutMontantLbs, AddressOf CalculDevise_US, TauxPourCalculDevise_us, devise_us)

                pc.STDPRDEscompteUsMontantLbs = CalculDevise_US(pc.CommandeEscompteTarif, TauxPourCalculDevise_us)
                If pc.CommandeID = 805619 Then
                    'pc.STDCMDEscompteUsMontantLbs = 0.1D
                End If
                's il existe un escompte, on calcul le prix initial avec le prix brut +escompte pour revenir au prix initial

                pc.STDPRDPrixVenteInitialUsBrutMontantLbs = pc.STDPRDPrixVenteUsBrutMontantLbs + pc.STDPRDEscompteUsMontantLbs
                pc.STDPRDRabaisVolumeUsMontantLbs = CalculDevise_US(cliHis.rabais_volume_lbs, TauxPourCalculDevise_us)
                pc.STDPRDResineUsMontantLbs = stdPvCout.cout
                If useNew AndAlso dat >= #2022-09-01# AndAlso stdCoutRejet IsNot Nothing Then
                    pc.STDPRDScrapUsMontantLbs = stdCoutRejet.cout_rejet_std_US_lb
                    'cNew += 1
                Else
                    pc.STDPRDScrapUsMontantLbs = stdCatSur.surcharge
                    'If dat >= #2022-09-01# Then cOld += 1
                End If
                pc.STDPRDVariableGlobaleUsMontantLbs = (vg.total + cliHis.cout_emballage) / stdTaux
                pc.STDPRDRabaisClientUsMontantLbs = CalculDevise_US(If(cliHis.rabais_actif AndAlso cliHis.rabais_type = 2, cliHis.rabais_montant, 0), TauxPourCalculDevise_us)
            End If

            ' Données FACTURATION

            Dim lstF As List(Of facture) = Nothing
            If dicFactureCmd.TryGetValue(pc.CommandeID, lstF) Then
                If lstF.First.date_facturation <> Nothing Then
                    pc.STDFACDate = lstF.First.date_facturation
                End If
            End If
            If pc.STDFACDate > CDate("1900-01-01") Then
                Dim dat = pc.STDFACDate
                stdPvCout = GetStdPvCout(pc.Pv, dat, pc.PvID)
                If useNew AndAlso dat >= DEBUT_NOUVEAU_CALCUL Then
                    stdCoutRejet = GetCoutRejet(cmd.id_pv, cmd.date_commande, cmd.poids, cmd.id_client, cmd.id_commande, dat.Value)
                    If stdCoutRejet Is Nothing Then Debug.Write("")
                Else
                    stdCatSur = GetStdCatSurcharge(stdPvCout.id_std_categorie, CInt(pc.CommandeLbs), dat)
                    'stdCatSur = GetStdCatSurcharge(stdPvCout.id_std_categorie, If(pc.ListePrixLbsChoisi <> 0, pc.ListePrixLbsChoisi, CInt(pc.CommandeLbs)), dat)
                End If
                stdTaux = GetStdTaux(dat)
                cliHis = GetClientHistorique(pc.ClientID, dat)
                vg = GetVarGlob(dat)

                pc.STDFACCategorieID = stdPvCout.id_std_categorie
                If useNew AndAlso dat >= #2022-09-01# AndAlso stdCoutRejet IsNot Nothing Then
                    pc.STDFACCategorieSurcharge = stdCoutRejet.id_std_categorie_surcharge
                    'cNew += 1
                Else
                    pc.STDFACCategorieSurcharge = stdCatSur.nom
                    'If dat >= #2022-09-01# Then cOld += 1
                End If
                pc.STDFACDateAnneeFiscale = AnneeFiscale(dat)
                pc.STDFACDateMoisFiscale = MoisFiscal(dat)
                pc.STDFACDateSemaineFiscale = SemaineFiscale(dat)
                pc.STDFACDateJourFiscale = JourFiscal(dat)
                pc.STDFACTauxUS = stdTaux

                pc.STDFACPrixVenteCanBrutMontantLbs = pc.CommandeMontantLbs * If(pc.CommandeDeviseID = 3, 1D, stdTaux)
                Dim TauxPourCalculDevise_can As Decimal = If(pc.CommandeDeviseID = 3, 1D, stdTaux)

                pc.STDFACCommissionVendeurCanMontantLbs = Calculer_Vendeur_Taux_Commission(pc.ClientVendeurID, dat, pc.STDFACPrixVenteCanBrutMontantLbs, AddressOf CalculDevise_Can, TauxPourCalculDevise_can, Devise_can)
                pc.STDFACRedevanceClientCanMontantLbs = Calculer_Client_Redevance(pc.ClientID, dat, pc.STDFACPrixVenteCanBrutMontantLbs, AddressOf CalculDevise_Can, TauxPourCalculDevise_can, Devise_can)

                pc.STDFACEscompteCanMontantLbs = CalculDevise_Can(pc.CommandeEscompteTarif, TauxPourCalculDevise_can)
                If pc.CommandeID = 805619 Then
                    'pc.STDFACEscompteUsMontantLbs = 0.1D
                End If
                's il existe un escompte, on calcul le prix initial avec le prix brut +escompte pour revenir au prix initial
                pc.STDFACPrixVenteInitialCanBrutMontantLbs = pc.STDFACPrixVenteCanBrutMontantLbs + pc.STDFACEscompteCanMontantLbs


                pc.STDFACRabaisVolumeCanMontantLbs = CalculDevise_Can(cliHis.rabais_volume_lbs, TauxPourCalculDevise_can)
                pc.STDFACResineCanMontantLbs = stdPvCout.cout * stdTaux
                If useNew AndAlso dat >= #2022-09-01# AndAlso stdCoutRejet IsNot Nothing Then
                    pc.STDFACScrapCanMontantLbs = stdCoutRejet.cout_rejet_std_US_lb * stdTaux
                    'cNew += 1
                Else
                    pc.STDFACScrapCanMontantLbs = stdCatSur.surcharge * stdTaux
                    'If dat >= #2022-09-01# Then cOld += 1
                End If
                pc.STDFACVariableGlobaleCanMontantLbs = (vg.total + cliHis.cout_emballage)
                pc.STDFACRabaisClientCanMontantLbs = CalculDevise_Can(If(cliHis.rabais_actif AndAlso cliHis.rabais_type = 2, cliHis.rabais_montant, 0), TauxPourCalculDevise_can)

                Dim TauxPourCalculDevise_us As Decimal = If(pc.CommandeDeviseID = 4, 1D, stdTaux)

                pc.STDFACPrixVenteUsBrutMontantLbs = CalculDevise_US(pc.CommandeMontantLbs, TauxPourCalculDevise_us)

                pc.STDFACCommissionVendeurUSMontantLbs = Calculer_Vendeur_Taux_Commission(pc.ClientVendeurID, dat, pc.STDFACPrixVenteUsBrutMontantLbs, AddressOf CalculDevise_US, TauxPourCalculDevise_us, devise_us)
                pc.STDFACRedevanceClientUsMontantLbs = Calculer_Client_Redevance(pc.ClientID, dat, pc.STDFACPrixVenteUsBrutMontantLbs, AddressOf CalculDevise_US, TauxPourCalculDevise_us, devise_us)

                pc.STDFACEscompteUsMontantLbs = CalculDevise_US(pc.CommandeEscompteTarif, TauxPourCalculDevise_us)
                If pc.CommandeID = 805619 Then
                    'pc.STDFACEscompteUsMontantLbs = 0.1D
                End If
                's il existe un escompte, on calcul le prix initial avec le prix brut +escompte pour revenir au prix initial
                pc.STDFACPrixVenteInitialUsBrutMontantLbs = pc.STDFACPrixVenteUsBrutMontantLbs + pc.STDFACEscompteUsMontantLbs


                pc.STDFACRabaisVolumeUsMontantLbs = CalculDevise_US(cliHis.rabais_volume_lbs, TauxPourCalculDevise_us)
                pc.STDFACResineUsMontantLbs = stdPvCout.cout
                If useNew AndAlso dat >= #2022-09-01# AndAlso stdCoutRejet IsNot Nothing Then
                    pc.STDFACScrapUsMontantLbs = stdCoutRejet.cout_rejet_std_US_lb
                    'cNew += 1
                Else
                    pc.STDFACScrapUsMontantLbs = stdCatSur.surcharge
                    'If dat >= #2022-09-01# Then cOld += 1
                End If
                pc.STDFACVariableGlobaleUsMontantLbs = (vg.total + cliHis.cout_emballage) / stdTaux
                pc.STDFACRabaisClientUsMontantLbs = CalculDevise_US(If(cliHis.rabais_actif AndAlso cliHis.rabais_type = 2, cliHis.rabais_montant, 0), TauxPourCalculDevise_us)
            End If

            ' Post Processing

            ' Section "Pondéré" (cmd vs produit_client par mois)
            If pc.CommandeStatutID <> 3 Then
                Dim period = pc.CommandeDate.Year * 100 + pc.CommandeDate.Month
                Dim key = GetDualIdKey(pc.ProduitClientID, period)
                If Not dicPexCmdPond.ContainsKey(key) Then dicPexCmdPond.Add(key, 0D)
                dicPexCmdPond(key) += pc.CommandeLbs
            End If

        Next

        Log($"stdCoutRejet: cOld: {cOld} / cNew: {cNew} / dOld: {dOld} / dNew: {dNew} / dWas: {dWas}")
        Debug.Write("")

    End Sub

    Private Sub CommandeStep5PostProcessing()

        ' Post-processing
        For Each pc In lstPexCmd
            If pc.CommandeStatutID <> 3 Then
                Dim period = pc.CommandeDate.Year * 100 + pc.CommandeDate.Month
                Dim key = GetDualIdKey(pc.ProduitClientID, period)
                Dim pond = 0D
                If dicPexCmdPond.TryGetValue(key, pond) AndAlso pond > 0D Then
                    pc.STDProduitClientVolumeObjectifPond = pc.STDProduitClientVolumeObjectif * pc.CommandeLbs / pond
                    pc.STDProduitClientVolumeObjectifReforecastPond = pc.STDProduitClientVolumeObjectifReforecast * pc.CommandeLbs / pond
                    pc.STDProduitClientVolumeObjectifRevisePond = pc.STDProduitClientVolumeObjectifRevise * pc.CommandeLbs / pond
                    pc.STDProduitClientVolumeObjectifReviseReforecastPond = pc.STDProduitClientVolumeObjectifReviseReforecast * pc.CommandeLbs / pond

                    pc.STDCmObjectifCanMontantLbs = pc.STDProduitCmObjCanMontantLbs + pc.STDProduitClientEscompteObjectifCanMontantLbs
                    pc.STDCmObjectifUsMontantLbs = pc.STDProduitCmObjUsMontantLbs + pc.STDProduitClientEscompteObjectifUsMontantLbs

                    pc.STDCmObjectifReviseCanMontantLbs = pc.STDProduitCmObjReviseCanMontantLbs + pc.STDProduitClientEscompteObjectifReviseCanMontantLbs
                    pc.STDCmObjectifReviseUsMontantLbs = pc.STDProduitCmObjReviseUsMontantLbs + pc.STDProduitClientEscompteObjectifReviseUsMontantLbs

                    pc.STDCmObjectifCanMontant = (pc.STDProduitCmObjCanMontantLbs + pc.STDProduitClientEscompteObjectifCanMontantLbs) * pc.STDProduitClientVolumeObjectifPond
                    pc.STDCmObjectifUsMontant = (pc.STDProduitCmObjUsMontantLbs + pc.STDProduitClientEscompteObjectifUsMontantLbs) * pc.STDProduitClientVolumeObjectifPond

                    pc.STDCmObjectifReforecastCanMontant = (pc.STDProduitCmObjCanMontantLbs + pc.STDProduitClientEscompteObjectifCanMontantLbs) * pc.STDProduitClientVolumeObjectifReforecastPond
                    pc.STDCmObjectifReforecastUsMontant = (pc.STDProduitCmObjUsMontantLbs + pc.STDProduitClientEscompteObjectifUsMontantLbs) * pc.STDProduitClientVolumeObjectifReforecastPond

                    pc.STDCmObjectifReviseCanMontant = (pc.STDProduitCmObjReviseCanMontantLbs + pc.STDProduitClientEscompteObjectifReviseCanMontantLbs) * pc.STDProduitClientVolumeObjectifRevisePond
                    pc.STDCmObjectifReviseUsMontant = (pc.STDProduitCmObjReviseUsMontantLbs + pc.STDProduitClientEscompteObjectifReviseUsMontantLbs) * pc.STDProduitClientVolumeObjectifRevisePond

                    pc.REELCmObjectifCanMontantLbs = pc.STDProduitCmObjCanMontantLbs + pc.STDProduitClientEscompteObjectifCanMontantLbs
                    pc.REELCmObjectifUsMontantLbs = pc.STDProduitCmObjUsMontantLbs + pc.STDProduitClientEscompteObjectifUsMontantLbs

                    pc.REELCmObjectifCanMontant = (pc.STDProduitCmObjCanMontantLbs + pc.STDProduitClientEscompteObjectifCanMontantLbs) * pc.STDProduitClientVolumeObjectifPond
                    pc.REELCmObjectifUsMontant = (pc.STDProduitCmObjUsMontantLbs + pc.STDProduitClientEscompteObjectifUsMontantLbs) * pc.STDProduitClientVolumeObjectifPond

                End If
            End If

            Dim lstPal As List(Of commande_palette) = Nothing
            If dicCmdPal_Cmd.TryGetValue(pc.CommandeID, lstPal) Then
                For Each pal In lstPal.Where(Function(x) x.id_expedition = 0 AndAlso x.emballee_flag AndAlso x.id_statut_palette = 0)
                    pc.CommandeNbPaletteEntrepot += 1
                    pc.CommandeQuantiteEntrepot += pal.poids_net
                Next
            End If

        Next

    End Sub

    Private Sub CommandeStep6RequisitionFacture()

        For Each pc In lstPexCmd

            ' Commande
            Dim cmdCT = GetStdCoutTransportAdresse(pc.CommandeAdresseID, pc.CommandeDate)
            Dim key = GetDualIdKey(pc.ClientID, pc.CommandeAdresseID)
            Dim cmdACH = GetAdresseClientHistorique(key, pc.CommandeDate)

            pc.STDCMDCoutTransportCanMontantLbs = If(cmdCT.cout_transport_standard_can, 0) 'cou_std ok
            pc.STDCMDCoutTransportUsMontantLbs = If(cmdCT.cout_transport_standard_us, 0)
            pc.STDCMDCoutTransportTauxUS = CDec(If(cmdCT.taux_us, 0)) '-fix?- cout a zero est weird... check pourquoi ca arrive
            'If Not cmdCT.taux_us.HasValue Then Debug.WriteLine($"### taux_us = 0: {pc.CommandeAdresseID}, {pc.CommandeDate}, {pc.CommandeID}")
            pc.STDCMDVariableClientCanMontantLbs = If(cmdCT.variable_client_can, 0) '+ (CDec(cmdACH.cout_entrepo_ext_us) * CDec(If(cmdCT.taux_us, 0)))
            pc.STDCMDVariableClientUsMontantLbs = If(cmdCT.variable_client_us, 0) '+ CDec(cmdACH.cout_entrepo_ext_us)

            ' Production
            Dim prdCT = GetStdCoutTransportAdresse(pc.CommandeAdresseID, pc.STDPRDDate)
            Dim prdACH = GetAdresseClientHistorique(key, pc.STDPRDDate)

            pc.STDPRDCoutTransportCanMontantLbs = If(prdCT.cout_transport_standard_can, 0) 'cou_std ok
            pc.STDPRDCoutTransportUsMontantLbs = If(prdCT.cout_transport_standard_us, 0)
            pc.STDPRDCoutTransportTauxUS = CDec(If(prdCT.taux_us, 0))
            'Debug.Assert(prdCT.taux_us.HasValue)
            pc.STDPRDVariableClientCanMontantLbs = If(prdCT.variable_client_can, 0) '+ (CDec(prdACH.cout_entrepo_ext_us) * CDec(If(prdCT.taux_us, 0)))
            pc.STDPRDVariableClientUsMontantLbs = If(prdCT.variable_client_us, 0) '+ CDec(prdACH.cout_entrepo_ext_us)

            ' Facturation
            Dim facCT = GetStdCoutTransportAdresse(pc.CommandeAdresseID, pc.STDFACDate)
            Dim facACH = GetAdresseClientHistorique(key, pc.STDFACDate)

            pc.STDFACCoutTransportCanMontantLbs = If(facCT.cout_transport_standard_can, 0) 'cou_std ok
            pc.STDFACCoutTransportUsMontantLbs = If(facCT.cout_transport_standard_us, 0)
            pc.STDFACCoutTransportTauxUS = CDec(If(facCT.taux_us, 0))
            'Debug.Assert(facCT.taux_us.HasValue)
            pc.STDFACVariableClientCanMontantLbs = If(facCT.variable_client_can, 0) '+ (CDec(facACH.cout_entrepo_ext_us) * CDec(If(facCT.taux_us, 0)))
            pc.STDFACVariableClientUsMontantLbs = If(facCT.variable_client_us, 0) '+ CDec(facACH.cout_entrepo_ext_us)

        Next

    End Sub

    Sub AddTotProdToPexCommande(pc As pexCommande, tp As totauxProduction)

        If pc.totProd Is Nothing Then pc.totProd = New totauxProduction

        Dim ptp = pc.totProd

        ptp.rou01_count += tp.rou01_count
        ptp.rou01_qte += tp.rou01_qte
        ptp.rou01_cou_can += tp.rou01_cou_can
        ptp.rou01_cou_us += tp.rou01_cou_us

        ptp.rou2_qte += tp.rou2_qte
        ptp.rou2_cou_scr_can += tp.rou2_cou_scr_can
        ptp.rou2_cou_scr_us += tp.rou2_cou_scr_us

        ptp.rou3_qte += tp.rou3_qte
        ptp.rou3_cou_scr_can += tp.rou3_cou_scr_can
        ptp.rou3_cou_scr_us += tp.rou3_cou_scr_us

        ptp.rou23_qte += tp.rou23_qte
        ptp.rou23_cou_can += tp.rou23_cou_can
        ptp.rou23_cou_us += tp.rou23_cou_us
        ptp.rou23_cou_rep_can += tp.rou23_cou_rep_can
        ptp.rou23_cou_rep_us += tp.rou23_cou_rep_us
        ptp.rou23_cou_ext_can += tp.rou23_cou_ext_can
        ptp.rou23_cou_ext_us += tp.rou23_cou_ext_us

        ptp.scrap_qte += tp.scrap_qte
        ptp.scrap_cou_can += tp.scrap_cou_can
        ptp.scrap_cou_us += tp.scrap_cou_us
        ptp.scrap_cou_rep_can += tp.scrap_cou_rep_can
        ptp.scrap_cou_ext_can += tp.scrap_cou_ext_can
        ptp.scrap_cou_rep_us += tp.scrap_cou_rep_us
        ptp.scrap_cou_ext_us += tp.scrap_cou_ext_us

        ptp.setup_qte += tp.setup_qte
        ptp.setup_cou_scr_can += tp.setup_cou_scr_can
        ptp.setup_cou_scr_us += tp.setup_cou_scr_us

        ptp.vrac_qte += tp.vrac_qte
        ptp.vrac_cou_scr_can += tp.vrac_cou_scr_can
        ptp.vrac_cou_scr_us += tp.vrac_cou_scr_us

    End Sub

    Function GetPexCommande(id_commande As Integer) As pexCommande
        Dim cmd As pexCommande = Nothing
        If Not dicPexCmd.TryGetValue(id_commande, cmd) Then
            cmd = New pexCommande With {.CommandeID = id_commande}

            lstPexCmd.Add(cmd)
            dicPexCmd.Add(id_commande, cmd)
        End If

        Return cmd
    End Function

    Function GetCommande(id_commande As Integer) As commande
        Dim cmd As commande = Nothing
        dicCommande.TryGetValue(id_commande, cmd)
        Return cmd
    End Function

    Sub CreateGroupCM() ' regrouper les CM par commande id
        ' new - use the pexCommandeMachine we just rebuilt in code
        Dim lstCMGroup = lstPexCM.GroupBy(Function(x) x.CommandeID)
        dicCMGroup = lstCMGroup.ToDictionary(Function(k) k.Key, Function(v) v.ToList)
    End Sub

End Module
