﻿Imports ProtoBuf

<PetaPoco.PrimaryKey("ID_STD_PV_RESINE_HISTORIQUE")>
<ProtoContract>
Partial Public Class std_pv_resine_historique
    <ProtoMember(1)> Public Property id_std_pv_resine_historique As Integer
    <ProtoMember(2)> Public Property id_std_pv_resine As Integer
    <ProtoMember(3)> Public Property pourc_resine As Double
    <ProtoMember(4)> Public Property resine_type_id As Integer
    <ProtoMember(5)> Public Property id_resine As Integer
    <ProtoMember(6)> Public Property actif As Boolean
    <ProtoMember(7)> Public Property date_debut As DateTime
    <ProtoMember(8)> Public Property date_fin As DateTime
End Class