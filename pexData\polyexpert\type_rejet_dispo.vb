﻿Imports ProtoBuf

<PetaPoco.PrimaryKey("id_type_rejet_dispo")>
<ProtoContract()>
Partial Public Class type_rejet_dispo
    'Implements IUtilisationResine
    <ProtoMember(1)> Public Property id_type_rejet_dispo As Integer
    <ProtoMember(2)> Public Property nom As String
    <ProtoMember(3)> Public Property ordre As Integer
    <ProtoMember(4)> Public Property recyclage As Boolean
    <ProtoMember(5)> Public Property grindage As Boolean
    <ProtoMember(6)> Public Property contamination As Boolean
    <ProtoMember(7)> Public Property id_type_rejet_resine_densite As Integer
    <ProtoMember(8)> Public Property id_type_rejet_resine_couleur As Integer
    <ProtoMember(9)> Public Property actif As Boolean
    <ProtoMember(10)> Public Property id_resine As Integer
End Class
