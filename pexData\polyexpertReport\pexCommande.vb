﻿Imports ProtoBuf

<ProtoContract()>
<PetaPoco.TableName("Commande")>
Partial Public Class pexCommande
    Implements ISortID
    <ProtoMember(1)> Public Property UniqueID As Long Implements ISortID.UniqueID
    <ProtoMember(2)> Public Property CommandeID As Integer Implements ISortID.SortID, ISortID.SortID2
    <ProtoMember(3)> Public Property Commande As String
    <ProtoMember(4)> Public Property CommandeVendeurID As Integer
    <ProtoMember(5)> Public Property CommandeVendeur As String
    <ProtoMember(6)> Public Property ClientID As Integer
    <ProtoMember(7)> Public Property Client As String
    <ProtoMember(8)> Public Property ClientTri As String
    <ProtoMember(9)> Public Property ClientTypeID As Integer
    <ProtoMember(10)> Public Property ClientType As String
    <ProtoMember(11)> Public Property ClientVendeurID As Integer
    <ProtoMember(12)> Public Property ClientVendeur As String
    <ProtoMember(13)> Public Property ProduitID As Integer
    <ProtoMember(14)> Public Property Produit As String
    <ProtoMember(15)> Public Property ProduitRegroupement As String
    <ProtoMember(16)> Public Property ProduitCategorie As String
    <ProtoMember(17)> Public Property ProduitTypeID As Integer
    <ProtoMember(18)> Public Property ProduitType As String
    <ProtoMember(19)> Public Property ProduitClientID As Integer
    <ProtoMember(20)> Public Property PvID As Integer
    <ProtoMember(21)> Public Property Pv As String
    <ProtoMember(22)> Public Property MarcheStrategique As String
    <ProtoMember(23)> Public Property Marche As String
    <ProtoMember(24)> Public Property SousMarche As String
    <ProtoMember(25)> Public Property SousMarcheSecondaire As String
    <ProtoMember(26)> Public Property TerritoireID As Integer
    <ProtoMember(27)> Public Property Territoire As String
    <ProtoMember(28)> Public Property MachineID As Integer
    <ProtoMember(29)> Public Property Machine As String
    <ProtoMember(30)> Public Property OrdreCedule As Integer
    <ProtoMember(31)> Public Property CommandeDate As DateTime
    <ProtoMember(32)> Public Property CommandeDateAnneeFiscale As Integer
    <ProtoMember(33)> Public Property CommandeDateMoisFiscale As Integer
    <ProtoMember(34)> Public Property CommandeDateSemaineFiscale As Integer
    <ProtoMember(35)> Public Property CommandeDateJourFiscale As Integer
    <ProtoMember(36)> Public Property CommandeLargeur As String
    <ProtoMember(37)> Public Property CommandeEpaisseur As Decimal
    <ProtoMember(38)> Public Property CommandeFormeID As Integer
    <ProtoMember(39)> Public Property CommandeForme As String
    <ProtoMember(40)> Public Property CommandeTraitement As String
    <ProtoMember(41)> Public Property CommandeLbs As Decimal
    <ProtoMember(42)> Public Property ListePrixLbsChoisi As Integer
    <ProtoMember(43)> Public Property CommandeEscompteTarif As Decimal
    <ProtoMember(44)> Public Property CommandeMontantLbs As Decimal
    <ProtoMember(45)> Public Property CommandeMontant As Decimal
    <ProtoMember(46)> Public Property CommandeDeviseID As Integer
    <ProtoMember(47)> Public Property CommandeDevise As String
    <ProtoMember(48)> Public Property CommandeAdresseID As Integer
    <ProtoMember(49)> Public Property CommandeSecteurID As Integer
    <ProtoMember(50)> Public Property CommandeSecteur As String
    <ProtoMember(51)> Public Property CommandeEchantillon As Boolean
    <ProtoMember(52)> Public Property CommandeStatutID As Integer
    <ProtoMember(53)> Public Property CommandeStatut As String
    <ProtoMember(54)> Public Property CommandeStatutFacturationID As Integer
    <ProtoMember(55)> Public Property CommandeStatutFacturation As String
    <ProtoMember(56)> Public Property CommandeStatutExpeditionID As Integer
    <ProtoMember(57)> Public Property CommandeStatutExpedition As String
    <ProtoMember(58)> Public Property CommandeNbRouleau As Integer
    <ProtoMember(59)> Public Property CommandeQuantiteEntrepot As Decimal
    <ProtoMember(60)> Public Property CommandeNbPaletteEntrepot As Integer
    <ProtoMember(61)> Public Property FactureLbs As Decimal
    <ProtoMember(62)> Public Property FactureMontant As Decimal
    <ProtoMember(63)> Public Property RetourLbs As Decimal
    <ProtoMember(64)> Public Property RetourMontant As Decimal
    <ProtoMember(65)> Public Property RetourPlainteDate As DateTime?
    <ProtoMember(66)> Public Property RetourPlainteCanMontant As Decimal
    <ProtoMember(67)> Public Property RetourPlainteUsMontant As Decimal
    <ProtoMember(68)> Public Property STDCMDCategorieID As Integer
    <ProtoMember(69)> Public Property STDCMDCategorieSurcharge As Integer
    <ProtoMember(70)> Public Property STDCMDCategorieSurchargePourcentageScrap As Decimal
    <ProtoMember(71)> Public Property STDCMDTauxUS As Decimal
    <ProtoMember(72)> Public Property STDCMDCoutTransportTauxUS As Decimal
    <ProtoMember(73)> Public Property STDCMDPrixVenteInitialCanBrutMontantLbs As Decimal
    <ProtoMember(74)> Public Property STDCMDEscompteCanMontantLbs As Decimal
    <ProtoMember(75)> Public Property STDCMDPrixVenteCanBrutMontantLbs As Decimal
    <ProtoMember(76)> Public Property STDCMDRabaisVolumeCanMontantLbs As Decimal
    <ProtoMember(77)> Public Property STDCMDCommissionVendeurCanMontantLbs As Decimal
    <ProtoMember(78)> Public Property STDCMDRedevanceClientCanMontantLbs As Decimal
    <ProtoMember(79)> Public Property STDCMDPrixVenteCanNetMontantLbs As Decimal
    <ProtoMember(80)> Public Property STDCMDResineCanMontantLbs As Decimal
    <ProtoMember(81)> Public Property STDCMDSpreadCanBrutMontantLbs As Decimal
    <ProtoMember(82)> Public Property STDCMDScrapCanMontantLbs As Decimal
    <ProtoMember(83)> Public Property STDCMDSpreadCanNetMontantLbs As Decimal
    <ProtoMember(84)> Public Property STDCMDVariableGlobaleCanMontantLbs As Decimal
    <ProtoMember(85)> Public Property STDCMDCmCanBrutMontantLbs As Decimal
    <ProtoMember(86)> Public Property STDCMDVariableClientCanMontantLbs As Decimal
    <ProtoMember(87)> Public Property STDCMDCoutTransportCanMontantLbs As Decimal
    <ProtoMember(88)> Public Property STDCMDCmCanNetMontantLbs As Decimal
    <ProtoMember(89)> Public Property STDCMDPrixVenteInitialUsBrutMontantLbs As Decimal
    <ProtoMember(90)> Public Property STDCMDEscompteUsMontantLbs As Decimal
    <ProtoMember(91)> Public Property STDCMDPrixVenteUsBrutMontantLbs As Decimal
    <ProtoMember(92)> Public Property STDCMDRabaisVolumeUsMontantLbs As Decimal
    <ProtoMember(93)> Public Property STDCMDCommissionVendeurUsMontantLbs As Decimal
    <ProtoMember(94)> Public Property STDCMDRedevanceClientUsMontantLbs As Decimal
    <ProtoMember(95)> Public Property STDCMDPrixVenteUsNetMontantLbs As Decimal
    <ProtoMember(96)> Public Property STDCMDResineUsMontantLbs As Decimal
    <ProtoMember(97)> Public Property STDCMDSpreadUsBrutMontantLbs As Decimal
    <ProtoMember(98)> Public Property STDCMDScrapUsMontantLbs As Decimal
    <ProtoMember(99)> Public Property STDCMDSpreadUsNetMontantLbs As Decimal
    <ProtoMember(100)> Public Property STDCMDVariableGlobaleUsMontantLbs As Decimal
    <ProtoMember(101)> Public Property STDCMDCmUsBrutMontantLbs As Decimal
    <ProtoMember(102)> Public Property STDCMDVariableClientUsMontantLbs As Decimal
    <ProtoMember(103)> Public Property STDCMDCoutTransportUsMontantLbs As Decimal
    <ProtoMember(104)> Public Property STDCMDCmUsNetMontantLbs As Decimal
    <ProtoMember(105)> Public Property STDCMDRabaisClientCanMontantLbs As Decimal
    <ProtoMember(106)> Public Property STDCMDRabaisClientUsMontantLbs As Decimal
    <ProtoMember(107)> Public Property STDPRDCategorieID As Integer
    <ProtoMember(108)> Public Property STDPRDCategorieSurcharge As Integer
    <ProtoMember(109)> Public Property STDPRDDate As DateTime?
    <ProtoMember(110)> Public Property STDPRDDateAnneeFiscale As Integer
    <ProtoMember(111)> Public Property STDPRDDateMoisFiscale As Integer
    <ProtoMember(112)> Public Property STDPRDDateSemaineFiscale As Integer
    <ProtoMember(113)> Public Property STDPRDDateJourFiscale As Integer
    <ProtoMember(114)> Public Property STDPRDTauxUS As Decimal
    <ProtoMember(115)> Public Property STDPRDCoutTransportTauxUS As Decimal
    <ProtoMember(116)> Public Property STDPRDPrixVenteInitialCanBrutMontantLbs As Decimal
    <ProtoMember(117)> Public Property STDPRDEscompteCanMontantLbs As Decimal
    <ProtoMember(118)> Public Property STDPRDPrixVenteCanBrutMontantLbs As Decimal
    <ProtoMember(119)> Public Property STDPRDRabaisVolumeCanMontantLbs As Decimal
    <ProtoMember(120)> Public Property STDPRDCommissionVendeurCanMontantLbs As Decimal
    <ProtoMember(121)> Public Property STDPRDRedevanceClientCanMontantLbs As Decimal
    <ProtoMember(122)> Public Property STDPRDPrixVenteCanNetMontantLbs As Decimal
    <ProtoMember(123)> Public Property STDPRDResineCanMontantLbs As Decimal
    <ProtoMember(124)> Public Property STDPRDSpreadCanBrutMontantLbs As Decimal
    <ProtoMember(125)> Public Property STDPRDScrapCanMontantLbs As Decimal
    <ProtoMember(126)> Public Property STDPRDSpreadCanNetMontantLbs As Decimal
    <ProtoMember(127)> Public Property STDPRDVariableGlobaleCanMontantLbs As Decimal
    <ProtoMember(128)> Public Property STDPRDCmCanBrutMontantLbs As Decimal
    <ProtoMember(129)> Public Property STDPRDVariableClientCanMontantLbs As Decimal
    <ProtoMember(130)> Public Property STDPRDCoutTransportCanMontantLbs As Decimal
    <ProtoMember(131)> Public Property STDPRDCmCanNetMontantLbs As Decimal
    <ProtoMember(132)> Public Property STDPRDPrixVenteInitialUsBrutMontantLbs As Decimal
    <ProtoMember(133)> Public Property STDPRDEscompteUsMontantLbs As Decimal
    <ProtoMember(134)> Public Property STDPRDPrixVenteUsBrutMontantLbs As Decimal
    <ProtoMember(135)> Public Property STDPRDRabaisVolumeUsMontantLbs As Decimal
    <ProtoMember(136)> Public Property STDPRDCommissionVendeurUsMontantLbs As Decimal
    <ProtoMember(137)> Public Property STDPRDRedevanceClientUsMontantLbs As Decimal
    <ProtoMember(138)> Public Property STDPRDPrixVenteUsNetMontantLbs As Decimal
    <ProtoMember(139)> Public Property STDPRDResineUsMontantLbs As Decimal
    <ProtoMember(140)> Public Property STDPRDSpreadUsBrutMontantLbs As Decimal
    <ProtoMember(141)> Public Property STDPRDScrapUsMontantLbs As Decimal
    <ProtoMember(142)> Public Property STDPRDSpreadUsNetMontantLbs As Decimal
    <ProtoMember(143)> Public Property STDPRDVariableGlobaleUsMontantLbs As Decimal
    <ProtoMember(144)> Public Property STDPRDCmUsBrutMontantLbs As Decimal
    <ProtoMember(145)> Public Property STDPRDVariableClientUsMontantLbs As Decimal
    <ProtoMember(146)> Public Property STDPRDCoutTransportUsMontantLbs As Decimal
    <ProtoMember(147)> Public Property STDPRDCmUsNetMontantLbs As Decimal
    <ProtoMember(148)> Public Property STDPRDRabaisClientCanMontantLbs As Decimal
    <ProtoMember(149)> Public Property STDPRDRabaisClientUsMontantLbs As Decimal
    <ProtoMember(150)> Public Property STDFACCategorieID As Integer
    <ProtoMember(151)> Public Property STDFACCategorieSurcharge As Integer
    <ProtoMember(152)> Public Property STDFACDate As DateTime?
    <ProtoMember(153)> Public Property STDFACDateAnneeFiscale As Integer
    <ProtoMember(154)> Public Property STDFACDateMoisFiscale As Integer
    <ProtoMember(155)> Public Property STDFACDateSemaineFiscale As Integer
    <ProtoMember(156)> Public Property STDFACDateJourFiscale As Integer
    <ProtoMember(157)> Public Property STDFACTauxUS As Decimal
    <ProtoMember(158)> Public Property STDFACCoutTransportTauxUS As Decimal
    <ProtoMember(159)> Public Property STDFACPrixVenteInitialCanBrutMontantLbs As Decimal
    <ProtoMember(160)> Public Property STDFACEscompteCanMontantLbs As Decimal
    <ProtoMember(161)> Public Property STDFACPrixVenteCanBrutMontantLbs As Decimal
    <ProtoMember(162)> Public Property STDFACRabaisVolumeCanMontantLbs As Decimal
    <ProtoMember(163)> Public Property STDFACCommissionVendeurCanMontantLbs As Decimal
    <ProtoMember(164)> Public Property STDFACRedevanceClientCanMontantLbs As Decimal
    <ProtoMember(165)> Public Property STDFACPrixVenteCanNetMontantLbs As Decimal
    <ProtoMember(166)> Public Property STDFACResineCanMontantLbs As Decimal
    <ProtoMember(167)> Public Property STDFACSpreadCanBrutMontantLbs As Decimal
    <ProtoMember(168)> Public Property STDFACScrapCanMontantLbs As Decimal
    <ProtoMember(169)> Public Property STDFACSpreadCanNetMontantLbs As Decimal
    <ProtoMember(170)> Public Property STDFACVariableGlobaleCanMontantLbs As Decimal
    <ProtoMember(171)> Public Property STDFACCmCanBrutMontantLbs As Decimal
    <ProtoMember(172)> Public Property STDFACVariableClientCanMontantLbs As Decimal
    <ProtoMember(173)> Public Property STDFACCoutTransportCanMontantLbs As Decimal
    <ProtoMember(174)> Public Property STDFACCmCanNetMontantLbs As Decimal
    <ProtoMember(175)> Public Property STDFACPrixVenteInitialUsBrutMontantLbs As Decimal
    <ProtoMember(176)> Public Property STDFACEscompteUsMontantLbs As Decimal
    <ProtoMember(177)> Public Property STDFACPrixVenteUsBrutMontantLbs As Decimal
    <ProtoMember(178)> Public Property STDFACRabaisVolumeUsMontantLbs As Decimal
    <ProtoMember(179)> Public Property STDFACCommissionVendeurUSMontantLbs As Decimal
    <ProtoMember(180)> Public Property STDFACRedevanceClientUsMontantLbs As Decimal
    <ProtoMember(181)> Public Property STDFACPrixVenteUsNetMontantLbs As Decimal
    <ProtoMember(182)> Public Property STDFACResineUsMontantLbs As Decimal
    <ProtoMember(183)> Public Property STDFACSpreadUsBrutMontantLbs As Decimal
    <ProtoMember(184)> Public Property STDFACScrapUsMontantLbs As Decimal
    <ProtoMember(185)> Public Property STDFACSpreadUsNetMontantLbs As Decimal
    <ProtoMember(186)> Public Property STDFACVariableGlobaleUsMontantLbs As Decimal
    <ProtoMember(187)> Public Property STDFACCmUsBrutMontantLbs As Decimal
    <ProtoMember(188)> Public Property STDFACVariableClientUsMontantLbs As Decimal
    <ProtoMember(189)> Public Property STDFACCoutTransportUsMontantLbs As Decimal
    <ProtoMember(190)> Public Property STDFACCmUsNetMontantLbs As Decimal
    <ProtoMember(191)> Public Property STDFACRabaisClientCanMontantLbs As Decimal
    <ProtoMember(192)> Public Property STDFACRabaisClientUsMontantLbs As Decimal
    <ProtoMember(193)> Public Property REELPrixVenteBrutTauxUS As Decimal
    <ProtoMember(194)> Public Property REELRabaisVolumeTauxUS As Decimal
    <ProtoMember(195)> Public Property REELResineTauxUs As Decimal
    <ProtoMember(196)> Public Property REELResineScrapTauxUs As Decimal
    <ProtoMember(197)> Public Property REELVariableGlobaleTauxUS As Decimal
    <ProtoMember(198)> Public Property REELVariableClientTauxUS As Decimal
    <ProtoMember(199)> Public Property REELPrixVenteInitialCanBrutMontantLbs As Decimal
    <ProtoMember(200)> Public Property REELEscompteCanMontantLbs As Decimal
    <ProtoMember(201)> Public Property REELPrixVenteCanBrutMontantLbs As Decimal
    <ProtoMember(202)> Public Property REELRabaisVolumeCanMontantLbs As Decimal
    <ProtoMember(203)> Public Property REELCommissionVendeurCanMontantLbs As Decimal
    <ProtoMember(204)> Public Property REELRedevanceClientCanMontantLbs As Decimal
    <ProtoMember(205)> Public Property REELPrixVenteCanNetMontantLbs As Decimal
    <ProtoMember(206)> Public Property REELQuantiteAjustementCanMontantLbs As Decimal
    <ProtoMember(207)> Public Property REELResineCanMontantLbs As Decimal
    <ProtoMember(208)> Public Property REELResineScrapCanMontantLbs As Decimal
    <ProtoMember(209)> Public Property REELSpreadCanBrutMontantLbs As Decimal
    <ProtoMember(210)> Public Property REELScrapCanMontantLbs As Decimal
    <ProtoMember(211)> Public Property REELSpreadCanNetMontantLbs As Decimal
    <ProtoMember(212)> Public Property REELVariableGlobaleCanMontantLbs As Decimal
    <ProtoMember(213)> Public Property REELCmCanBrutMontantLbs As Decimal
    <ProtoMember(214)> Public Property REELVariableClientCanMontantLbs As Decimal
    <ProtoMember(215)> Public Property REELCmCanNetMontantLbs As Decimal
    <ProtoMember(216)> Public Property REELPrixVenteInitialUsBrutMontantLbs As Decimal
    <ProtoMember(217)> Public Property REELEscompteUsMontantLbs As Decimal
    <ProtoMember(218)> Public Property REELPrixVenteUsBrutMontantLbs As Decimal
    <ProtoMember(219)> Public Property REELRabaisVolumeUsMontantLbs As Decimal
    <ProtoMember(220)> Public Property REELCommissionVendeurUsMontantLbs As Decimal
    <ProtoMember(221)> Public Property REELRedevanceClientUsMontantLbs As Decimal
    <ProtoMember(222)> Public Property REELPrixVenteUsNetMontantLbs As Decimal
    <ProtoMember(223)> Public Property REELQuantiteAjustementUsMontantLbs As Decimal
    <ProtoMember(224)> Public Property REELResineUsMontantLbs As Decimal
    <ProtoMember(225)> Public Property REELResineScrapUsMontantLbs As Decimal
    <ProtoMember(226)> Public Property REELSpreadUsBrutMontantLbs As Decimal
    <ProtoMember(227)> Public Property REELScrapUsMontantLbs As Decimal
    <ProtoMember(228)> Public Property REELSpreadUsNetMontantLbs As Decimal
    <ProtoMember(229)> Public Property REELVariableGlobaleUsMontantLbs As Decimal
    <ProtoMember(230)> Public Property REELCmUsBrutMontantLbs As Decimal
    <ProtoMember(231)> Public Property REELVariableClientUsMontantLbs As Decimal
    <ProtoMember(232)> Public Property REELCmUsNetMontantLbs As Decimal
    <ProtoMember(233)> Public Property REELProductionBrute As Decimal
    <ProtoMember(234)> Public Property REELProductionExpediableReel As Decimal
    <ProtoMember(235)> Public Property REELAjustementExpediable As Decimal
    <ProtoMember(236)> Public Property REELProductionExpediable As Decimal
    <ProtoMember(237)> Public Property REELNbRouleauExpediable As Integer
    <ProtoMember(238)> Public Property REELRejetSetup As Decimal
    <ProtoMember(239)> Public Property REELRejetSetupCan As Decimal
    <ProtoMember(240)> Public Property REELRejetSetupUs As Decimal
    <ProtoMember(241)> Public Property REELRejetVrac As Decimal
    <ProtoMember(242)> Public Property REELRejetVracCan As Decimal
    <ProtoMember(243)> Public Property REELRejetVracUs As Decimal
    <ProtoMember(244)> Public Property REELRejetNCR As Decimal
    <ProtoMember(245)> Public Property REELRejetNCRCan As Decimal
    <ProtoMember(246)> Public Property REELRejetNCRUs As Decimal
    <ProtoMember(247)> Public Property REELRejetNCH As Decimal
    <ProtoMember(248)> Public Property REELRejetNCHCan As Decimal
    <ProtoMember(249)> Public Property REELRejetNCHUs As Decimal
    <ProtoMember(250)> Public Property REELRejetTotal As Decimal
    <ProtoMember(251)> Public Property REELRejetTotalCan As Decimal
    <ProtoMember(252)> Public Property REELRejetTotalUs As Decimal
    <ProtoMember(253)> Public Property REELRejetTotalReprocessCan As Decimal
    <ProtoMember(254)> Public Property REELRejetTotalReprocessUs As Decimal
    <ProtoMember(255)> Public Property REELRejetTotalExtrusionCan As Decimal
    <ProtoMember(256)> Public Property REELRejetTotalExtrusionUs As Decimal
    <ProtoMember(257)> Public Property REELRejetPourcentage As Decimal
    <ProtoMember(258)> Public Property REELTransportCanMontantLbs As Decimal
    <ProtoMember(259)> Public Property REELTransportUsMontantLbs As Decimal
    <ProtoMember(260)> Public Property REELEntreposageCanMontantLbs As Decimal
    <ProtoMember(261)> Public Property REELEntreposageUsMontantLbs As Decimal
    <ProtoMember(262)> Public Property REELDouaneAssuranceCanMontantLbs As Decimal
    <ProtoMember(263)> Public Property REELDouaneAssuranceUsMontantLbs As Decimal
    <ProtoMember(264)> Public Property REELCreditCanMontantLbs As Decimal
    <ProtoMember(265)> Public Property REELCreditUsMontantLbs As Decimal
    <ProtoMember(266)> Public Property REELRabaisVolumeAutreCanMontantLbs As Decimal
    <ProtoMember(267)> Public Property REELRabaisVolumeAutreUsMontantLbs As Decimal
    <ProtoMember(268)> Public Property REELEntretienReparationCanMontantLbs As Decimal
    <ProtoMember(269)> Public Property REELEntretienReparationUsMontantLbs As Decimal
    <ProtoMember(270)> Public Property REELElectriciteCanMontantLbs As Decimal
    <ProtoMember(271)> Public Property REELElectriciteUsMontantLbs As Decimal
    <ProtoMember(272)> Public Property REELFraisVenteCanMontantLbs As Decimal
    <ProtoMember(273)> Public Property REELFraisVenteUsMontantLbs As Decimal
    <ProtoMember(274)> Public Property REELInteretCanMontantLbs As Decimal
    <ProtoMember(275)> Public Property REELInteretUsMontantLbs As Decimal
    <ProtoMember(276)> Public Property REELMauvaiseCreanceCanMontantLbs As Decimal
    <ProtoMember(277)> Public Property REELMauvaiseCreanceUsMontantLbs As Decimal
    <ProtoMember(278)> Public Property REELExpeditionCanMontantLbs As Decimal
    <ProtoMember(279)> Public Property REELExpeditionUsMontantLbs As Decimal
    <ProtoMember(280)> Public Property REELModCanMontantLbs As Decimal
    <ProtoMember(281)> Public Property REELModUsMontantLbs As Decimal
    <ProtoMember(282)> Public Property REELRabaisClientCanMontantLbs As Decimal
    <ProtoMember(283)> Public Property REELRabaisClientUsMontantLbs As Decimal
    <ProtoMember(284)> Public Property STDProduitClientVolumeObjectif As Decimal
    <ProtoMember(285)> Public Property STDProduitClientVolumeObjectifRevise As Decimal
    <ProtoMember(286)> Public Property STDProduitClientVolumeObjectifReforecast As Decimal
    <ProtoMember(287)> Public Property STDProduitClientVolumeObjectifReviseReforecast As Decimal
    <ProtoMember(288)> Public Property STDProduitClientVolumeObjectifPond As Decimal
    <ProtoMember(289)> Public Property STDProduitClientVolumeObjectifRevisePond As Decimal
    <ProtoMember(290)> Public Property STDProduitClientVolumeObjectifReforecastPond As Decimal
    <ProtoMember(291)> Public Property STDProduitClientVolumeObjectifReviseReforecastPond As Decimal
    <ProtoMember(292)> Public Property STDProduitCmObjCanMontantLbs As Decimal
    <ProtoMember(293)> Public Property STDProduitCmObjReviseCanMontantLbs As Decimal
    <ProtoMember(294)> Public Property STDProduitCmObjUsMontantLbs As Decimal
    <ProtoMember(295)> Public Property STDProduitCmObjReviseUsMontantLbs As Decimal
    <ProtoMember(296)> Public Property STDProduitClientEscompteObjectifCanMontantLbs As Decimal
    <ProtoMember(297)> Public Property STDProduitClientEscompteObjectifReviseCanMontantLbs As Decimal
    <ProtoMember(298)> Public Property STDProduitClientEscompteObjectifUsMontantLbs As Decimal
    <ProtoMember(299)> Public Property STDProduitClientEscompteObjectifReviseUsMontantLbs As Decimal
    <ProtoMember(300)> Public Property STDCmObjectifCanMontantLbs As Decimal
    <ProtoMember(301)> Public Property STDCmObjectifReviseCanMontantLbs As Decimal
    <ProtoMember(302)> Public Property STDCmObjectifUsMontantLbs As Decimal
    <ProtoMember(303)> Public Property STDCmObjectifReviseUsMontantLbs As Decimal
    <ProtoMember(304)> Public Property STDCmObjectifCanMontant As Decimal
    <ProtoMember(305)> Public Property STDCmObjectifReviseCanMontant As Decimal
    <ProtoMember(306)> Public Property STDCmObjectifUsMontant As Decimal
    <ProtoMember(307)> Public Property STDCmObjectifReviseUsMontant As Decimal
    <ProtoMember(308)> Public Property REELCmObjectifCanMontantLbs As Decimal
    <ProtoMember(309)> Public Property REELCmObjectifUsMontantLbs As Decimal
    <ProtoMember(310)> Public Property REELCmObjectifCanMontant As Decimal
    <ProtoMember(311)> Public Property REELCmObjectifUsMontant As Decimal
    <ProtoMember(312)> Public Property Calculer As Boolean
    <ProtoMember(313)> Public Property CommandeRetD As Boolean
    <ProtoMember(314)> Public Property REELVariableGlobaleCanMontantLbsTmp As Decimal
    <ProtoMember(315)> Public Property REELVariableGlobaleUsMontantLbsTmp As Decimal
    <ProtoMember(316)> Public Property REELResineSTDUsMontantLbs As Decimal
    <ProtoMember(317)> Public Property REELResineListeUsMontantLbs As Decimal
    <ProtoMember(318)> Public Property STDCmObjectifReforecastCanMontant As Decimal
    <ProtoMember(319)> Public Property STDCmObjectifReforecastUsMontant As Decimal
    <ProtoMember(320)> Public Property STDVCTransportSecteurvsHistorique As String
    <ProtoMember(321)> Public Property STDVCTransportInclus As Boolean
    <ProtoMember(322)> Public Property STDVCTransportSecteurCanMontantLbs As Decimal
    <ProtoMember(323)> Public Property STDVCTransportSecteurUsMontantLbs As Decimal
    <ProtoMember(324)> Public Property STDVCTransportHistoriqueCanMontantLbs As Decimal
    <ProtoMember(325)> Public Property STDVCTransportHistoriqueUsMontantLbs As Decimal
    <ProtoMember(326)> Public Property STDVCTransportCanMontantLbs As Decimal
    <ProtoMember(327)> Public Property STDVCTransportUsMontantLbs As Decimal
    <ProtoMember(328)> Public Property STDVCDouaneCanMontantLbs As Decimal
    <ProtoMember(329)> Public Property STDVCDouaneUsMontantLbs As Decimal
    <ProtoMember(330)> Public Property STDVCEntreposageCanMontantLbs As Decimal
    <ProtoMember(331)> Public Property STDVCEntreposageUsMontantLbs As Decimal
    <ProtoMember(332)> Public Property STDVCRabaisClientCanMontantLbs As Decimal
    <ProtoMember(333)> Public Property STDVCRabaisClientUsMontantLbs As Decimal
    <ProtoMember(334)> Public Property STDVCEntrepotExterneCanMontantLbs As Decimal
    <ProtoMember(335)> Public Property STDVCEntrepotExterneUsMontantLbs As Decimal
    <ProtoMember(336)> Public Property STDVCTransportSecteurCanMontant As Decimal
    <ProtoMember(337)> Public Property STDVCTransportSecteurUsMontant As Decimal
    <ProtoMember(338)> Public Property STDVCTransportHistoriqueCanMontant As Decimal
    <ProtoMember(339)> Public Property STDVCTransportHistoriqueUsMontant As Decimal
    <ProtoMember(340)> Public Property STDVCTransportCanMontant As Decimal
    <ProtoMember(341)> Public Property STDVCTransportUsMontant As Decimal
    <ProtoMember(342)> Public Property STDVCDouaneCanMontant As Decimal
    <ProtoMember(343)> Public Property STDVCDouaneUsMontant As Decimal
    <ProtoMember(344)> Public Property STDVCEntreposageCanMontant As Decimal
    <ProtoMember(345)> Public Property STDVCEntreposageUsMontant As Decimal
    <ProtoMember(346)> Public Property STDVCRabaisClientCanMontant As Decimal
    <ProtoMember(347)> Public Property STDVCRabaisClientUsMontant As Decimal
    <ProtoMember(348)> Public Property STDVCEntrepotExterneCanMontant As Decimal
    <ProtoMember(349)> Public Property STDVCEntrepotExterneUsMontant As Decimal
    <ProtoMember(350)> Public Property REELVCTransportCanMontantLbs As Decimal
    <ProtoMember(351)> Public Property REELVCTransportUsMontantLbs As Decimal
    <ProtoMember(352)> Public Property REELVCDouaneCanMontantLbs As Decimal
    <ProtoMember(353)> Public Property REELVCDouaneUsMontantLbs As Decimal
    <ProtoMember(354)> Public Property REELVCEntreposageCanMontantLbs As Decimal
    <ProtoMember(355)> Public Property REELVCEntreposageUsMontantLbs As Decimal
    <ProtoMember(356)> Public Property REELVCRabaisClientCanMontantLbs As Decimal
    <ProtoMember(357)> Public Property REELVCRabaisClientUsMontantLbs As Decimal
    <ProtoMember(358)> Public Property REELVCEntrepotExterneCanMontantLbs As Decimal
    <ProtoMember(359)> Public Property REELVCEntrepotExterneUsMontantLbs As Decimal
    <ProtoMember(360)> Public Property REELRevenuTransportCanMontantLbs As Decimal
    <ProtoMember(361)> Public Property REELRevenuTransportUSMontantLbs As Decimal
    <ProtoMember(362)> Public Property REELPrixVenteBrutInclusTransportCanMontantLbs As Decimal
    <ProtoMember(363)> Public Property REELPrixVenteBrutInclusTransportUSMontantLbs As Decimal
    <ProtoMember(364)> Public Property REELRabaisTransportFactureCanMontantLbs As Decimal
    <ProtoMember(365)> Public Property REELRabaisTransportFactureUSMontantLbs As Decimal
    <ProtoMember(366)> Public Property REELPrixVenteCanMontantLbs As Decimal
    <ProtoMember(367)> Public Property REELPrixVenteUSMontantLbs As Decimal
    <PetaPoco.Ignore> Public Property totProd As totauxProduction
End Class