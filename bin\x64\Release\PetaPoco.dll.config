<?xml version="1.0"?>
<configuration>
	<connectionStrings>
		<add name="mysql" connectionString="server=***;database=***;user id=***;password=***;Allow User Variables=true" providerName="MySql.Data.MySqlClient"/>
    <add name="sqlserverce" connectionString="Data Source=C:\Users\<USER>\dev\Source\PetaPoco\PetaPoco.Tests\petapoco.sdf" providerName="System.Data.SqlServerCe.4.0"/>
    <add name="postgresql" connectionString="Server=127.0.0.1;User id=postgres;password=password01;Database=tempdb;" providerName="Npgsql"/>
  </connectionStrings>

<startup><supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.7.2"/></startup></configuration>
