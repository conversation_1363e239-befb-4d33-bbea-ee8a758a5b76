﻿Imports ProtoBuf

<ProtoContract>
<PetaPoco.TableName("EMPLOYE")>                 ' use the exact table name in your DB
<PetaPoco.PrimaryKey("ID_EMPLOYE")>
Partial Public Class employe
    <ProtoMember(1)> Public Property id_employe As Integer
    <ProtoMember(2)> Public Property nom As String
    <ProtoMember(3)> Public Property prenom As String
    <ProtoMember(4)> Public Property STD_COUT_CAN_HEURE As Decimal?
    <ProtoMember(5)> Public Property nb_heures_hebdo_payees As Integer?
    <ProtoMember(6)> Public Property actif As Boolean?
End Class
