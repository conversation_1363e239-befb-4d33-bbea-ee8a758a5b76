﻿Imports System.IO
Imports System.Threading
Imports AsyncIO
Imports NetMQ
Imports NetMQ.Sockets
Imports UtilitiesData

Module NetMQ

    Public Sub NetMQStart()

        Dim thread = New Thread(AddressOf NetMQServer)
        thread.IsBackground = True ' do not prevent process from terminating
        thread.Start()

    End Sub

    ' See: https://mikaelkoskinen.net/post/netmq-and-creating-a-dynamic-worker-per-task

    Private Sub NetMQServer()

        Dim bytesCM = New Byte() {}
        Dim bytesCmd = New Byte() {}
        Dim bytesFac = New Byte() {}
        Dim bytesRF = New Byte() {}

        Using server = New ResponseSocket($"@tcp://localhost:{NMQ_PORT}") ' bind		  

            Do
                ' Receive the message from the server socket
                Dim msg = server.ReceiveFrameString()
                Console.WriteLine($"From Client: {msg}")

                Select Case msg
                    Case "GetListeCheck", "GetListePexCommandeMachine"
                        If bytesCM.Length = 0 Then
                            Using ms = New MemoryStream
                                ProtoStreamFromList(dicPexCM.Values, False).CopyTo(ms)
                                bytesCM = ms.ToArray
                            End Using
                        End If
                        server.SendFrame(bytesCM) '-todo- support pour refresh des données.

                    Case "GetListePexCommande"
                        If bytesCmd.Length = 0 Then
                            Using ms = New MemoryStream
                                ProtoStreamFromList(lstPexCmd, False).CopyTo(ms)
                                bytesCmd = ms.ToArray
                            End Using
                        End If
                        server.SendFrame(bytesCmd) '-todo- support pour refresh des données.

                    Case "GetListePexFacture"
                        If bytesFac.Length = 0 Then
                            Using ms = New MemoryStream
                                ProtoStreamFromList(lstPexFac, False).CopyTo(ms)
                                bytesFac = ms.ToArray
                            End Using
                        End If
                        server.SendFrame(bytesFac) '-todo- support pour refresh des données.

                    Case "GetListePexRequisitionFacture"
                        If bytesRF.Length = 0 Then
                            Using ms = New MemoryStream
                                ProtoStreamFromList(lstPexRF, False).CopyTo(ms)
                                bytesRF = ms.ToArray
                            End Using
                        End If
                        server.SendFrame(bytesRF) '-todo- support pour refresh des données.

                    Case Else
                        Console.WriteLine("Unknown request")
                End Select

            Loop '-todo- way to cancel thread...(?) Background thread will be terminated with process

        End Using

    End Sub

End Module
