﻿Imports Utilities
Public Module pexReportCFacture

    Public lstPexFac As List(Of pexFacture)
    Public dicPexFac As Dictionary(Of Integer, pexFacture)
    Private dicPexCommande As Dictionary(Of Integer, pexCommande)

    Public Sub CleanMemory()
        lstPexFac.Clear()
        lstPexFac = Nothing
        dicPexFac.Clear()
        dicPexFac = Nothing
        dicPexCommande.Clear()
        dicPexCommande = Nothing
    End Sub

    Public Sub BuildFacture()

        Using New Chrono(Sub(ms) Log($"BuildFacture done. Elapsed: {ms:#,0}ms"), Sub() Log("BuildFacture started...", color:=ConsoleColor.White))

            lstPexFac = New List(Of pexFacture)
            dicPexFac = New Dictionary(Of Integer, pexFacture)
            dicPexCommande = lstPexCmd.ToDictionary(Function(k) k.CommandeID, Function(v) v)

            ProcessFacturesStep1Factures()
            ProcessFacturesStep2Retours()
            ProcessFacturesStep3Annulations()
            ProcessFacturesStep4PostProcessing()
            UpdateCommandesFromFacturesStep1() ' REEL
            UpdateCommandesFromFacturesStep2() ' Plaintes
            UpdateCommandesFromFacturesStep3() ' Retours
            ProcessFacturesStep5PostProcessing() ' Reel, StdCM, ReelCM...

        End Using

    End Sub

    Public Sub UpdateCommandesFromFacturesStep1()

        Dim dicFac = lstPexFac.GroupBy(Function(f) f.CommandeID) _
                              .ToDictionary(Function(k) k.Key, Function(v) v.ToList)

        For Each pc In lstPexCmd
            Dim facs As List(Of pexFacture) = Nothing
            If dicFac.TryGetValue(pc.CommandeID, facs) Then
                Dim sumFacLbs = facs.Sum(Function(f) f.FactureLbs)

                pc.FactureLbs = sumFacLbs
                pc.FactureMontant = facs.Sum(Function(f) f.FactureMontant)

                If sumFacLbs <> 0 Then
                    '-reel-new
                    pc.REELPrixVenteCanMontantLbs = facs.Sum(Function(x) x.FactureMontant * If(x.FactureDeviseID = 3, 1D, x.FactureTaux)) / sumFacLbs
                    pc.REELPrixVenteUSMontantLbs = facs.Sum(Function(x) x.FactureMontant / If(x.FactureDeviseID = 4, 1D, If(x.FactureTaux = 0, 1, x.FactureTaux))) / sumFacLbs

                    pc.REELRabaisVolumeCanMontantLbs = facs.Sum(Function(x) x.REELRabaisVolumeCanMontantLbs * x.FactureLbs) / sumFacLbs
                    pc.REELRabaisVolumeUsMontantLbs = facs.Sum(Function(x) x.REELRabaisVolumeUsMontantLbs * x.FactureLbs) / sumFacLbs
                    pc.REELRabaisVolumeTauxUS = facs.Sum(Function(x) x.FactureTaux * x.FactureLbs) / sumFacLbs

                    pc.REELVariableGlobaleCanMontantLbs = facs.Sum(Function(x) x.REELVariableGlobaleCanMontantLbs * x.FactureLbs) / sumFacLbs
                    pc.REELVariableGlobaleUsMontantLbs = facs.Sum(Function(x) x.REELVariableGlobaleUsMontantLbs * x.FactureLbs) / sumFacLbs
                    pc.REELVariableGlobaleTauxUS = facs.Sum(Function(x) x.FactureTaux * x.FactureLbs) / sumFacLbs

                    pc.REELRabaisClientCanMontantLbs = facs.Sum(Function(x) x.REELRabaisClientCanMontantLbs * x.FactureLbs) / sumFacLbs
                    pc.REELRabaisClientUsMontantLbs = facs.Sum(Function(x) x.REELRabaisClientUsMontantLbs * x.FactureLbs) / sumFacLbs
                End If

            End If

        Next

    End Sub

    Public Sub UpdateCommandesFromFacturesStep2()

        Dim dicPlt = lstPlainte.GroupBy(Function(p) p.id_commande) _
                               .ToDictionary(Function(k) k.Key, Function(v) v.ToList)

        Dim dicFacPlt = lstPexFac.Where(Function(x) x.FacturePlainteEntenteID > 0) _
                                 .GroupBy(Function(f) f.FacturePlainteEntenteID) _
                                 .ToDictionary(Function(k) k.Key, Function(v) v.ToList)

        For Each pc In lstPexCmd

            ' MAJ des crédits 
            Dim lstPlt As List(Of plainte) = Nothing
            Dim sumFacMnt = 0D
            Dim retPltDate As DateTime = Nothing
            Dim retPltCanMnt = 0D
            Dim retPltUsMnt = 0D
            If dicPlt.TryGetValue(pc.CommandeID, lstPlt) Then
                For Each plt In lstPlt
                    Dim lstEnt As List(Of plainte_entente) = Nothing
                    If dicPlainteEntenteByPlt.TryGetValue(plt.id_plainte, lstEnt) Then
                        For Each ent In lstEnt
                            Dim lstFac As List(Of pexFacture) = Nothing
                            If dicFacPlt.TryGetValue(ent.id_plainte_entente, lstFac) Then
                                For Each fac In lstFac
                                    sumFacMnt += fac.FactureMontant
                                    If fac.FactureDate > retPltDate Then retPltDate = fac.FactureDate
                                    retPltCanMnt += fac.FactureMontant * If(fac.FactureDeviseID = 3, 1D, fac.FactureTaux)
                                    retPltUsMnt += fac.FactureMontant / If(fac.FactureDeviseID = 4, 1D, If(fac.FactureTaux = 0, 1, fac.FactureTaux))
                                Next
                            End If
                        Next
                    End If
                Next
            End If
            If sumFacMnt > 0 Then
                pc.RetourPlainteDate = retPltDate
                pc.RetourPlainteCanMontant = retPltCanMnt
                pc.RetourPlainteUsMontant = retPltUsMnt
            End If

        Next

    End Sub

    Public Sub UpdateCommandesFromFacturesStep3()

        For Each pf In lstPexFac
            If pf.CommandeID < 0 Then
                Dim pc = GetPexCommande(-pf.CommandeID)
                pc.RetourLbs += pf.FactureLbs
                pc.RetourMontant += pf.FactureMontant / If(pf.FactureDeviseID = 4, 1D, If(pf.FactureTaux = 0, 1D, pf.FactureTaux))
            End If
        Next

    End Sub

    Public Sub ProcessFacturesStep5PostProcessing()

        For Each pf In lstPexFac

            Dim pexCmd = TryGetValue(dicPexCommande, pf.CommandeID)
            Dim bTransfertRouleaux = False
            Dim items As List(Of facture_item) = Nothing
            '
            ' New 2025-08-28: Pour chaque facture, on veut la machine "principale" de production
            ' En géréral, il n'y a qu'une seule machine pour la production sur une facture.
            ' S'il y en a plusieurs, on veut celle avec la plus grande production.
            Dim dicMP As New Dictionary(Of Integer, Decimal) ' Machine principale: id_machine, total qte_lb_production_expediable

            If dicFactureItem_Fac.TryGetValue(pf.FactureID, items) Then
                For Each itm In items
                    Dim palettes As List(Of commande_palette) = Nothing
                    If dicCmdPal_Itm.TryGetValue(itm.id_facture_item, palettes) Then
                        For Each pal In palettes
                            Dim rouleaux As List(Of rouleau) = Nothing
                            If dicRouleau_Pal.TryGetValue(pal.id_commande_palette, rouleaux) Then
                                For Each rou In rouleaux
                                    If rou.totProd IsNot Nothing Then
                                        AddTotProdToPexFacture(pf, rou.totProd)
                                        ' Machine principale
                                        If dicMP.ContainsKey(rou.id_machine) Then
                                            dicMP(rou.id_machine) += pf.totProd.rou01_qte ' production expédiable
                                        Else
                                            dicMP(rou.id_machine) = pf.totProd.rou01_qte
                                        End If
                                    Else
                                        Console.WriteLine($"rouleau {rou.id_rouleau}: no totProd")
                                    End If
                                Next
                            End If
                        Next
                    End If
                Next

            End If

            ' Machine principale
            If dicMP.Count > 0 Then
                If dicMP.Count > 1 Then Debug.Write("")
                Dim maxProdMachID = dicMP.OrderByDescending(Function(kvp) kvp.Value).First.Key
                pf.MachinePrincipaleID = maxProdMachID
                pf.MachinePrincipale = dicMachine(maxProdMachID).nom
            End If

            Dim tp = pf.totProd
            If tp IsNot Nothing Then

                Dim gcm As groupCM = Nothing
                Dim CmdList = TryGetValue(dicCmdMacMult, pf.CommandeID)
                If CmdList.Count > 0 Then
                    For Each cmd In CmdList
                        If ccdCM.TryGetValue(GetDualIdKey(cmd.id_commande_machine, cmd.id_commande), gcm) Then
                            Dim factureDivREELProductionExpediable As Decimal = Div0(pf.FactureLbs, pexCmd.REELProductionExpediable)
                            tp.setup_qte += (gcm.totProd.setup_qte * factureDivREELProductionExpediable)
                            tp.setup_cou_scr_can += (gcm.totProd.setup_cou_scr_can * factureDivREELProductionExpediable)
                            tp.setup_cou_scr_us += (gcm.totProd.setup_cou_scr_us * factureDivREELProductionExpediable)

                            tp.scrap_qte += (gcm.totProd.scrap_qte * factureDivREELProductionExpediable)
                            tp.scrap_cou_can += (gcm.totProd.scrap_cou_can * factureDivREELProductionExpediable)
                            tp.scrap_cou_us += (gcm.totProd.scrap_cou_us * factureDivREELProductionExpediable)

                            tp.vrac_qte += (gcm.totProd.vrac_qte * factureDivREELProductionExpediable)
                            tp.vrac_cou_scr_can += (gcm.totProd.vrac_cou_scr_can * factureDivREELProductionExpediable)
                            tp.vrac_cou_scr_us += (gcm.totProd.vrac_cou_scr_us * factureDivREELProductionExpediable)

                            tp.scrap_cou_ext_can += (gcm.totProd.scrap_cou_ext_can * factureDivREELProductionExpediable)
                            tp.scrap_cou_ext_us += (gcm.totProd.scrap_cou_ext_us * factureDivREELProductionExpediable)
                            tp.scrap_cou_rep_can += (gcm.totProd.scrap_cou_rep_can * factureDivREELProductionExpediable)
                            tp.scrap_cou_rep_us += (gcm.totProd.scrap_cou_rep_us * factureDivREELProductionExpediable)

                            tp.rou23_qte += (gcm.totProd.rou23_qte * factureDivREELProductionExpediable)
                            tp.rou23_cou_can += (gcm.totProd.rou23_cou_can * factureDivREELProductionExpediable)
                            tp.rou23_cou_us += (gcm.totProd.rou23_cou_us * factureDivREELProductionExpediable)
                            tp.rou23_cou_ext_can += (gcm.totProd.rou23_cou_ext_can * factureDivREELProductionExpediable)
                            tp.rou23_cou_ext_us += (gcm.totProd.rou23_cou_ext_us * factureDivREELProductionExpediable)
                            tp.rou23_cou_rep_can += (gcm.totProd.rou23_cou_rep_can * factureDivREELProductionExpediable)
                            tp.rou23_cou_rep_us += (gcm.totProd.rou23_cou_rep_us * factureDivREELProductionExpediable)

                            tp.rou2_qte += (gcm.totProd.rou2_qte * factureDivREELProductionExpediable)
                            tp.rou2_cou_scr_can += (gcm.totProd.rou2_cou_scr_can * factureDivREELProductionExpediable)
                            tp.rou2_cou_scr_us += (gcm.totProd.rou2_cou_scr_us * factureDivREELProductionExpediable)
                            tp.rou3_qte += (gcm.totProd.rou3_qte * factureDivREELProductionExpediable)
                            tp.rou3_cou_scr_can += (gcm.totProd.rou3_cou_scr_can * factureDivREELProductionExpediable)
                            tp.rou3_cou_scr_us += (gcm.totProd.rou3_cou_scr_us * factureDivREELProductionExpediable)
                        End If
                    Next
                End If

                pf.REELResineCanMontantLbs = Div0(tp.rou01_cou_can, tp.rou01_qte)
                pf.REELResineUsMontantLbs = Div0(tp.rou01_cou_us, tp.rou01_qte)
                pf.REELResineTauxUs = Div0(pf.REELResineCanMontantLbs, pf.REELResineUsMontantLbs)

                Dim REELResineScrapCanMontantLbs = Div0((tp.rou23_cou_can + tp.scrap_cou_can), (tp.rou23_qte + tp.scrap_qte))
                Dim REELResineScrapUsMontantLbs = Div0((tp.rou23_cou_us + tp.scrap_cou_us), (tp.rou23_qte + tp.scrap_qte))

                pf.REELResineScrapTauxUs = Div0(pf.REELScrapCanMontantLbs, pf.REELScrapUsMontantLbs)

                pf.REELProductionExpediable = tp.rou01_qte

                Dim REELRejetSetup = tp.setup_qte
                Dim REELRejetSetupCan = tp.setup_cou_scr_can
                Dim REELRejetSetupUs = tp.setup_cou_scr_us

                Dim REELRejetVrac = tp.vrac_qte
                Dim REELRejetVracCan = tp.vrac_cou_scr_can
                Dim REELRejetVracUs = tp.vrac_cou_scr_us

                Dim REELRejetNCR = tp.rou3_qte
                Dim REELRejetNCRCan = tp.rou3_cou_scr_can
                Dim REELRejetNCRUs = tp.rou3_cou_scr_us

                Dim REELRejetNCH = tp.rou2_qte
                Dim REELRejetNCHCan = tp.rou2_cou_scr_can
                Dim REELRejetNCHUs = tp.rou2_cou_scr_us

                Dim REELRejetTotalExtrusionCan = tp.rou23_cou_ext_can + tp.scrap_cou_ext_can
                Dim REELRejetTotalExtrusionUs = tp.rou23_cou_ext_us + tp.scrap_cou_ext_us
                Dim REELRejetTotalReprocessCan = tp.rou23_cou_rep_can + tp.scrap_cou_rep_can
                Dim REELRejetTotalReprocessUs = tp.rou23_cou_rep_us + tp.scrap_cou_rep_us

                ' Champs dérivés
                pf.REELRejetTotal = (REELRejetSetup + REELRejetVrac + REELRejetNCH + REELRejetNCR)

                Dim REELRejetTotalCan = (REELRejetSetupCan + REELRejetVracCan + REELRejetNCHCan + REELRejetNCRCan)
                Dim REELRejetTotalUs = (REELRejetSetupUs + REELRejetVracUs + REELRejetNCHUs + REELRejetNCRUs)

                ' Description du calcul:
                ' A) on prend le cout de la scrap basé sur la strucure (cout réel de la scrap selon les résines du mélange)
                ' B) on ajoute le cout du reprocess et de l'extrusion (couts fixes selon std_variable_globale)
                ' C) on soustrait le cout de la résine scrap (la valeur que l'on attribue a ce qui reste après le reprocess
                ' D) on obtient ainsi combien nous a couté la scrap produit (cout initial selon qte*résine + processus - valeur après récupération)
                pf.REELScrapCanMontant = (pf.REELRejetTotal * REELResineScrapCanMontantLbs) + REELRejetTotalReprocessCan + REELRejetTotalExtrusionCan - REELRejetTotalCan

                pf.REELScrapCanMontantLbs = Div0(pf.REELScrapCanMontant, pf.REELProductionExpediable)
                pf.REELScrapUsMontant = (pf.REELRejetTotal * REELResineScrapUsMontantLbs) + REELRejetTotalReprocessUs + REELRejetTotalExtrusionUs - REELRejetTotalUs
                pf.REELScrapUsMontantLbs = Div0(pf.REELScrapUsMontant, pf.REELProductionExpediable)

                pf.BudgetSetupLb = tp.setup_budget

            End If

        Next

    End Sub

    Public Sub ProcessFacturesStep1Factures()

        For Each fac In lstFacture.Where(Function(x) x.type = "Facture")

            Dim cmd = TryGetValue(dicCommande, fac.id_commande)
            Dim pexCmd = TryGetValue(dicPexCommande, cmd.id_commande)
            Dim cli = TryGetValue(dicClient, fac.id_client)
            Dim items = TryGetValue(dicFactureItem_Fac, fac.id_facture)

            Dim tc As taux_change
            If fac.taux_echange > 0 Then
                tc = New taux_change With {.id_taux_change = -1, .datejour = fac.date_facturation.Date, .taux = fac.taux_echange}
            Else
                tc = TryGetValue(dicTauxChange, fac.date_facturation)
            End If

            Dim pv = TryGetValue(dicPV, cmd.id_pv)
            Dim prd = TryGetValue(dicProduit, pv.id_produit)
            Dim prdCli = TryGetValue(dicProduitClient, GetDualIdKey(pv.id_produit, pv.id_client))

            Dim spc = TryGetValue(dicStdProduitClient, (prdCli.id_produit_client, fac.date_facturation.Year, fac.date_facturation.Month))
            Dim spr = TryGetValue(dicStdProduit, (prdCli.id_produit_client, fac.date_facturation.Year, fac.date_facturation.Month))
            Dim cmdCTA = GetStdCoutTransportAdresse(fac.id_adresse, fac.date_commande)
            Dim cmdCTS = GetStdCoutTransportAdresse(cmdCTA.id_secteur_transport, fac.date_commande)

            Dim pf = GetPexFacture(fac.id_facture)

            pf.CommandeID = fac.id_commande
            pf.ClientID = fac.id_client
            pf.Facture = fac.no_facture
            pf.ProduitID = prd.id_produit
            pf.Produit = prd.nom
            pf.ProduitClientID = prdCli.id_produit_client
            pf.PvID = pv.id_pv
            pf.Pv = pv.chaine_pv
            pf.FactureDate = fac.date_facturation
            pf.FactureDateAnneeFiscale = AnneeFiscale(fac.date_facturation)
            pf.FactureDateMoisFiscale = MoisFiscal(fac.date_facturation)
            pf.FactureTaux = tc.taux
            pf.FactureDeviseID = fac.id_devise
            pf.FactureDevise = If(fac.id_devise = 4, "US", "CAN")

            '* Exclure les factures annulées.
            If Not fac.annule Then
                ' Totaux pour les items
                Dim facItems = items.Where(Function(x) Not PRD_EXCLUS_POUR_CALCUL_COUT.Contains(("" & x.code_produit).ToUpperInvariant)).ToList
                Dim totFactureLbs = facItems.Sum(Function(x) GetPoidsLb(x.unite, CDec(x.quantite_expedie), cmd.longueur, cmd.poids, CDec(cmd.poids_rouleau_cal), x.code_produit))
                Dim totFactureMontantLbs = 0D
                Dim totPoidsLb = 0D
                Dim totRetour = 0D
                Dim maxIdTypeRetour = -1

                If totFactureLbs <> 0 Then
                    For Each item In facItems
                        Dim tmp1 = GetPrixLb(item.unite, item.prix, cmd.longueur, cmd.poids, CDec(cmd.poids_rouleau_cal), item.code_produit)
                        Dim tmp2 = GetPoidsLb(item.unite, CDec(item.quantite_expedie), cmd.longueur, cmd.poids, CDec(cmd.poids_rouleau_cal), "")
                        totFactureMontantLbs += tmp1 * tmp2
                        totPoidsLb += If(PRD_EXCLUS_POUR_CALCUL_POIDS.Contains(("" & item.code_produit).ToUpperInvariant), 0D, tmp2)

                        ' compter les rouleaux en même temps, évite de faire une deuxième passe dans les items.
                        Dim palettes As List(Of commande_palette) = Nothing
                        If dicCmdPal_Itm.TryGetValue(item.id_facture_item, palettes) Then
                            For Each pal In palettes
                                Dim rouleaux As List(Of rouleau) = Nothing
                                If dicRouleau_Pal.TryGetValue(pal.id_commande_palette, rouleaux) Then
                                    pf.FactureNbRouleau += rouleaux.Count
                                End If

                            Next
                        End If

                    Next
                    totFactureMontantLbs = totFactureMontantLbs / totPoidsLb
                End If
                pf.FactureInitialLbs = totFactureLbs
                pf.FactureInitialMontant = totFactureLbs * totFactureMontantLbs
                pf.FactureMontantLbs = totFactureMontantLbs
                pf.FactureType = fac.type
                pf.FactureRetourID = fac.id_facture_retour
                pf.FacturePlainteEntenteID = fac.id_plainte_entente

                pf.REELPrixVenteCanMontantLbs = totFactureMontantLbs * If(fac.id_devise = 3, 1, tc.taux)
                pf.REELPrixVenteUSMontantLbs = totFactureMontantLbs / If(fac.id_devise = 4, 1, If(tc.taux = 0D, 1D, tc.taux))
                pf.REELPrixVenteBrutTauxUS = tc.taux
                pexCmd.REELPrixVenteBrutTauxUS = tc.taux
                pf.REELRabaisVolumeTauxUS = tc.taux
                pf.REELVariableGlobaleTauxUS = tc.taux

                pf.STDProduitClientVolumeObjectif = spc.obj_volume
                pf.STDProduitCmObjCanMontantLbs = spr.obj_cm_can
                pf.STDProduitCmObjUsMontantLbs = spr.obj_cm_us
                pf.STDProduitClientEscompteObjectifCanMontantLbs = spc.obj_escompte_can
                pf.STDProduitClientEscompteObjectifUsMontantLbs = spc.obj_escompte_us

                pf.STDProduitClientVolumeObjectifRevise = spc.obj_volume_revise
                pf.STDProduitClientEscompteObjectifReviseCanMontantLbs = spc.obj_escompte_revise_can
                pf.STDProduitClientEscompteObjectifReviseUsMontantLbs = spc.obj_escompte_revise_us
                pf.STDProduitCmObjReviseCanMontantLbs = spr.obj_cm_revise_can
                pf.STDProduitCmObjReviseUsMontantLbs = spr.obj_cm_revise_us

                'Dim FactureMensuel = TryGetValue(dicFactureMensuel, spc.id_produit_client.ToString() + "_" + Year(pf.FactureDate).ToString() + "_" + Month(pf.FactureDate).ToString())
                Dim FactureMensuel = TryGetValue(dicFactureMensuel, $"{spc.id_produit_client}_{Year(pf.FactureDate)}_{Month(pf.FactureDate)}")
                If FactureMensuel IsNot Nothing Then
                    If FactureMensuel.FactureLbs > 0 Then pf.STDProduitClientVolumeObjectifPond = pf.STDProduitClientVolumeObjectif * (totFactureLbs / FactureMensuel.FactureLbs)
                    If FactureMensuel.FactureLbs > 0 Then pf.STDProduitClientVolumeObjectifRevisePond = pf.STDProduitClientVolumeObjectifRevise * (totFactureLbs / FactureMensuel.FactureLbs)
                End If
                pf.STDCmObjectifCanMontantLbs = pf.STDProduitCmObjCanMontantLbs + pf.STDProduitClientEscompteObjectifCanMontantLbs
                pf.STDCmObjectifUsMontantLbs = pf.STDProduitCmObjUsMontantLbs + pf.STDProduitClientEscompteObjectifUsMontantLbs
                pf.STDCmObjectifReviseCanMontantLbs = pf.STDProduitCmObjReviseCanMontantLbs + pf.STDProduitClientEscompteObjectifReviseCanMontantLbs
                pf.STDCmObjectifReviseUsMontantLbs = pf.STDProduitCmObjReviseUsMontantLbs + pf.STDProduitClientEscompteObjectifReviseUsMontantLbs
                pf.STDCmObjectifCanMontant = (pf.STDProduitCmObjCanMontantLbs + pf.STDProduitClientEscompteObjectifCanMontantLbs) * pf.STDProduitClientVolumeObjectifPond
                pf.STDCmObjectifUsMontant = (pf.STDProduitCmObjUsMontantLbs + pf.STDProduitClientEscompteObjectifUsMontantLbs) * pf.STDProduitClientVolumeObjectifPond
                pf.STDCmObjectifReviseCanMontant = (pf.STDProduitCmObjReviseCanMontantLbs + pf.STDProduitClientEscompteObjectifReviseCanMontantLbs) * pf.STDProduitClientVolumeObjectifRevisePond
                pf.STDCmObjectifReviseUsMontant = (pf.STDProduitCmObjReviseUsMontantLbs + pf.STDProduitClientEscompteObjectifReviseUsMontantLbs) * pf.STDProduitClientVolumeObjectifRevisePond
                pf.REELCmObjectifCanMontantLbs = pf.STDProduitCmObjCanMontantLbs + pf.STDProduitClientEscompteObjectifCanMontantLbs
                pf.REELCmObjectifUsMontantLbs = pf.STDProduitCmObjUsMontantLbs + pf.STDProduitClientEscompteObjectifUsMontantLbs
                pf.REELCmObjectifCanMontant = (pf.STDProduitCmObjCanMontantLbs + pf.STDProduitClientEscompteObjectifCanMontantLbs) * pf.STDProduitClientVolumeObjectifPond
                pf.REELCmObjectifUsMontant = (pf.STDProduitCmObjUsMontantLbs + pf.STDProduitClientEscompteObjectifUsMontantLbs) * pf.STDProduitClientVolumeObjectifPond

                If cmdCTA.id_cout_transport_source_calcul = 1 Then '-todo- utiliser la description de la table STD_COUT_TRANSPORT_TYPE_CALCUL
                    pf.STDVCTransportSecteurvsHistorique = "Liste secteur histo"
                ElseIf cmdCTA.id_cout_transport_source_calcul = 2 Then
                    pf.STDVCTransportSecteurvsHistorique = "Histo 12 mois secteur"
                ElseIf cmdCTA.id_cout_transport_source_calcul = 3 Then
                    pf.STDVCTransportSecteurvsHistorique = "Histo 12 mois adresse"
                ElseIf cmdCTA.id_cout_transport_source_calcul = 4 Then
                    pf.STDVCTransportSecteurvsHistorique = "Histo 12 mois client"
                Else
                    pf.STDVCTransportSecteurvsHistorique = ""
                End If

                'If Not cmdCT.client_paie_transport Or cmdCT.prix_vente_inclut_transport Then
                If cmdCTA.client_paie_transport = True Or cmdCTA.prix_vente_inclut_transport = False Then
                    pf.STDVCTransportInclus = False
                Else
                    pf.STDVCTransportInclus = True
                End If
                pf.STDVCTransportSecteurCanMontantLbs = If(cmdCTS.cout_transport_standard_can, 0) 'cou_std
                pf.STDVCTransportSecteurUsMontantLbs = If(cmdCTS.cout_transport_standard_us, 0)
                If Not cmdCTA.cout_transport_standard_can.HasValue Then
                    pf.STDVCTransportHistoriqueCanMontantLbs = 0
                    pf.STDVCTransportHistoriqueUsMontantLbs = 0
                    pf.STDVCTransportHistoriqueCanMontant = 0
                    pf.STDVCTransportHistoriqueUsMontant = 0
                Else
                    pf.STDVCTransportHistoriqueCanMontantLbs = If(cmdCTA.cout_transport_standard_can, 0) 'cou_std
                    pf.STDVCTransportHistoriqueUsMontantLbs = If(cmdCTA.cout_transport_standard_us, 0)
                    pf.STDVCTransportHistoriqueCanMontant = pf.STDVCTransportHistoriqueCanMontantLbs * pf.FactureLbs
                    pf.STDVCTransportHistoriqueUsMontant = pf.STDVCTransportHistoriqueUsMontantLbs * pf.FactureLbs
                End If
                pf.STDVCTransportCanMontantLbs = If(cmdCTA.cout_transport_standard_can, 0) 'cou_std
                pf.STDVCTransportUsMontantLbs = If(cmdCTA.cout_transport_standard_us, 0)
                pf.STDVCDouaneCanMontantLbs = If(cmdCTA.douane_assurance_can, 0)
                pf.STDVCDouaneUsMontantLbs = If(cmdCTA.douane_assurance_us, 0)
                pf.STDVCEntreposageCanMontantLbs = If(cmdCTA.entreposage_can, 0)
                pf.STDVCEntreposageUsMontantLbs = If(cmdCTA.entreposage_us, 0)
                pf.STDVCRabaisClientCanMontantLbs = If(cmdCTA.rabais_client_can, 0)
                pf.STDVCRabaisClientUsMontantLbs = If(cmdCTA.rabais_client_us, 0)
                pf.STDVCEntrepotExterneCanMontantLbs = If(cmdCTA.entrepot_externe_can, 0)
                pf.STDVCEntrepotExterneUsMontantLbs = If(cmdCTA.entrepot_externe_us, 0)

                pf.STDVCTransportCanMontant = pf.STDVCTransportCanMontantLbs * pf.FactureLbs
                pf.STDVCTransportUsMontant = pf.STDVCTransportUsMontantLbs * pf.FactureLbs
                pf.STDVCTransportSecteurCanMontant = pf.STDVCTransportSecteurCanMontantLbs * pf.FactureLbs
                pf.STDVCTransportSecteurUsMontant = pf.STDVCTransportSecteurUsMontantLbs * pf.FactureLbs

                pf.STDVCDouaneCanMontant = pf.STDVCDouaneCanMontantLbs * pf.FactureLbs
                pf.STDVCDouaneUsMontant = pf.STDVCDouaneUsMontantLbs * pf.FactureLbs
                pf.STDVCEntreposageCanMontant = pf.STDVCEntreposageCanMontantLbs * pf.FactureLbs
                pf.STDVCEntreposageUsMontant = pf.STDVCEntreposageUsMontantLbs * pf.FactureLbs
                pf.STDVCRabaisClientCanMontant = pf.STDVCRabaisClientCanMontantLbs * pf.FactureLbs
                pf.STDVCRabaisClientUsMontant = pf.STDVCRabaisClientUsMontantLbs * pf.FactureLbs
                pf.STDVCEntrepotExterneCanMontant = pf.STDVCEntrepotExterneCanMontantLbs * pf.FactureLbs
                pf.STDVCEntrepotExterneUsMontant = pf.STDVCEntrepotExterneUsMontantLbs * pf.FactureLbs

            End If
        Next

    End Sub

    Public Sub ProcessFacturesStep2Retours()

        For Each fac In lstFacture.Where(Function(x) x.type = "Retour")

            Dim items = TryGetValue(dicFactureItem_Fac, fac.id_facture)
            Dim pf = GetPexFacture(fac.id_facture)
            Dim fo As facture = Nothing
            dicFacture.TryGetValue(fac.id_facture_retour, fo)
            Dim cli = TryGetValue(dicClient, fac.id_client)
            Dim cmd = TryGetValue(dicCommande, If(fac.id_commande > 0, fac.id_commande, If(fo Is Nothing, 0, fo.id_commande)))
            Dim tc = TryGetValue(dicTauxChange, fac.date_facturation)

            pf.CommandeID = -cmd.id_commande ' pourquoi le négatif? (est comme ça depuis spRemplirFacture...)
            'pf.CommandeID = cmd.id_commande
            pf.ClientID = fac.id_client
            pf.Facture = fac.no_facture
            pf.FactureDate = fac.date_facturation
            pf.FactureDateAnneeFiscale = AnneeFiscale(fac.date_facturation)
            pf.FactureDateMoisFiscale = MoisFiscal(fac.date_facturation)
            pf.FactureTaux = tc.taux
            pf.FactureDeviseID = fac.id_devise
            pf.FactureDevise = If(fac.id_devise = 4, "US", "CAN")

            pf.FactureInitialLbs = items.Sum(Function(x) GetPoidsLb(x.unite, CDec(x.quantite_expedie), cmd.longueur, cmd.poids, CDec(cmd.poids_rouleau_cal), x.code_produit))
            pf.FactureInitialMontant = items.Sum(Function(x) x.quantite_expedie * x.prix)
            pf.FactureMontantLbs = 0D
            pf.FactureNbRouleau = 0
            pf.FactureType = fac.type
            pf.FactureRetourID = fac.id_facture_retour
            pf.FacturePlainteEntenteID = fac.id_plainte_entente

        Next

    End Sub

    Public Sub ProcessFacturesStep3Annulations()

        For Each facRet In lstPexFac.Where(Function(x) x.FactureType = "Retour")
            ' trouver les items associées
            Dim items As List(Of facture_item) = Nothing
            If dicFactureItem_Fac.TryGetValue(facRet.FactureID, items) Then
                If items.Max(Function(x) x.id_type_retour) = 6 Then '-check- -retour- this code is weird
                    Dim facOrig As pexFacture = Nothing
                    If dicPexFac.TryGetValue(facRet.FactureRetourID, facOrig) Then
                        facOrig.FactureAnnulationLbs = facRet.FactureInitialLbs
                        facOrig.FactureAnnulationMontant = facRet.FactureInitialMontant
                    End If
                End If
            End If
        Next

    End Sub

    Public Sub ProcessFacturesStep4PostProcessing()

        Dim dicPexCommande As Dictionary(Of Integer, pexCommande)
        dicPexCommande = lstPexCmd.ToDictionary(Function(k) k.CommandeID, Function(v) v)
        For Each pf In lstPexFac

            Dim ch = GetClientHistorique(pf.ClientID, pf.FactureDate)
            Dim svg = GetVarGlob(pf.FactureDate)

            'If pf.ProduitClientID = 9045 AndAlso pf.FactureDate >= #2022-10-01# AndAlso pf.FactureDate < #2022-11-01# Then
            '    Console.WriteLine($"{pf.FactureDate} {pf.Facture} {pf.FactureInitialLbs} {pf.FactureAnnulationLbs}")
            '    Debug.Write("")
            'End If


            pf.FactureLbs = pf.FactureInitialLbs - pf.FactureAnnulationLbs
            pf.FactureMontant = pf.FactureInitialMontant - pf.FactureAnnulationMontant
            pf.REELRabaisVolumeCanMontantLbs = ch.rabais_volume_lbs * If(pf.FactureDeviseID = 3, 1D, pf.FactureTaux)
            pf.REELRabaisVolumeUsMontantLbs = ch.rabais_volume_lbs / If(pf.FactureDeviseID = 4, 1D, If(pf.FactureTaux = 0, 1D, pf.FactureTaux))
            pf.REELVariableGlobaleCanMontantLbs = svg.total + ch.cout_emballage
            pf.REELVariableGlobaleUsMontantLbs = If(pf.REELVariableGlobaleTauxUS <> 0D, (svg.total + ch.cout_emballage) / pf.REELVariableGlobaleTauxUS, 0D)
            pf.REELRabaisClientCanMontantLbs = pf.REELRabaisTransportFactureCanMontantLbs ' -fix- champ REELRabaisClientCanMontantLbs à supprimer
            pf.REELRabaisClientUsMontantLbs = pf.REELRabaisTransportFactureUSMontantLbs ' -fix- champ REELRabaisClientCanMontantLbs à supprimer

            '***
            'Dim FactureMensuel = TryGetValue(dicFactureMensuel, pf.ProduitClientID.ToString() + "_" + Year(pf.FactureDate).ToString() + "_" + Month(pf.FactureDate).ToString())
            Dim FactureMensuel = TryGetValue(dicFactureMensuel, $"{pf.ProduitClientID}_{Year(pf.FactureDate)}_{Month(pf.FactureDate)}")
            If FactureMensuel IsNot Nothing Then
                If FactureMensuel.FactureLbs > 0 Then pf.STDProduitClientVolumeObjectifPond = pf.STDProduitClientVolumeObjectif * (pf.FactureLbs / FactureMensuel.FactureLbs)
                If FactureMensuel.FactureLbs > 0 Then pf.STDProduitClientVolumeObjectifRevisePond = pf.STDProduitClientVolumeObjectifRevise * (pf.FactureLbs / FactureMensuel.FactureLbs)
            End If
            pf.STDCmObjectifCanMontantLbs = pf.STDProduitCmObjCanMontantLbs + pf.STDProduitClientEscompteObjectifCanMontantLbs
            pf.STDCmObjectifUsMontantLbs = pf.STDProduitCmObjUsMontantLbs + pf.STDProduitClientEscompteObjectifUsMontantLbs
            pf.STDCmObjectifReviseCanMontantLbs = pf.STDProduitCmObjReviseCanMontantLbs + pf.STDProduitClientEscompteObjectifReviseCanMontantLbs
            pf.STDCmObjectifReviseUsMontantLbs = pf.STDProduitCmObjReviseUsMontantLbs + pf.STDProduitClientEscompteObjectifReviseUsMontantLbs
            pf.STDCmObjectifCanMontant = (pf.STDProduitCmObjCanMontantLbs + pf.STDProduitClientEscompteObjectifCanMontantLbs) * pf.STDProduitClientVolumeObjectifPond
            pf.STDCmObjectifUsMontant = (pf.STDProduitCmObjUsMontantLbs + pf.STDProduitClientEscompteObjectifUsMontantLbs) * pf.STDProduitClientVolumeObjectifPond
            pf.STDCmObjectifReviseCanMontant = (pf.STDProduitCmObjReviseCanMontantLbs + pf.STDProduitClientEscompteObjectifReviseCanMontantLbs) * pf.STDProduitClientVolumeObjectifRevisePond
            pf.STDCmObjectifReviseUsMontant = (pf.STDProduitCmObjReviseUsMontantLbs + pf.STDProduitClientEscompteObjectifReviseUsMontantLbs) * pf.STDProduitClientVolumeObjectifRevisePond
            pf.REELCmObjectifCanMontantLbs = pf.STDProduitCmObjCanMontantLbs + pf.STDProduitClientEscompteObjectifCanMontantLbs
            pf.REELCmObjectifUsMontantLbs = pf.STDProduitCmObjUsMontantLbs + pf.STDProduitClientEscompteObjectifUsMontantLbs
            pf.REELCmObjectifCanMontant = (pf.STDProduitCmObjCanMontantLbs + pf.STDProduitClientEscompteObjectifCanMontantLbs) * pf.STDProduitClientVolumeObjectifPond
            pf.REELCmObjectifUsMontant = (pf.STDProduitCmObjUsMontantLbs + pf.STDProduitClientEscompteObjectifUsMontantLbs) * pf.STDProduitClientVolumeObjectifPond
            '***

            Dim pexCmd = TryGetValue(dicPexCommande, pf.CommandeID)
            If pexCmd IsNot Nothing Then
                '    If pexCmd.CommandeID = 793555 Then Debug.Write("")
                pf.CommandeLbs = pexCmd.CommandeLbs * Div0(pf.FactureLbs, pexCmd.FactureLbs)
            End If
        Next

        'Debug.Write("")

    End Sub

    Function GetPexFacture(id_facture As Integer) As pexFacture
        Dim pf As pexFacture = Nothing
        If Not dicPexFac.TryGetValue(id_facture, pf) Then
            pf = New pexFacture With {.FactureID = id_facture}
            lstPexFac.Add(pf)
            dicPexFac.Add(id_facture, pf)
            End If
            Return pf
    End Function

    Function GetFacture(id_facture As Integer) As facture
        Dim fac As facture = Nothing
        dicFacture.TryGetValue(id_facture, fac)
        Return fac
    End Function

    Private Sub AddTotProdToPexFacture(pf As pexFacture, tp As totauxProduction)

        If pf.totProd Is Nothing Then pf.totProd = New totauxProduction

        Dim ptp = pf.totProd

        ptp.rou01_count += tp.rou01_count
        ptp.rou01_qte += tp.rou01_qte
        ptp.rou01_cou_can += tp.rou01_cou_can
        ptp.rou01_cou_us += tp.rou01_cou_us

        ptp.rou2_qte += tp.rou2_qte
        ptp.rou2_cou_scr_can += tp.rou2_cou_scr_can
        ptp.rou2_cou_scr_us += tp.rou2_cou_scr_us

        ptp.rou3_qte += tp.rou3_qte
        ptp.rou3_cou_scr_can += tp.rou3_cou_scr_can
        ptp.rou3_cou_scr_us += tp.rou3_cou_scr_us

        ptp.rou23_qte += tp.rou23_qte
        ptp.rou23_cou_can += tp.rou23_cou_can
        ptp.rou23_cou_us += tp.rou23_cou_us
        ptp.rou23_cou_rep_can += tp.rou23_cou_rep_can
        ptp.rou23_cou_rep_us += tp.rou23_cou_rep_us
        ptp.rou23_cou_ext_can += tp.rou23_cou_ext_can
        ptp.rou23_cou_ext_us += tp.rou23_cou_ext_us

        ptp.scrap_qte += tp.scrap_qte
        ptp.scrap_cou_can += tp.scrap_cou_can
        ptp.scrap_cou_us += tp.scrap_cou_us
        ptp.scrap_cou_rep_can += tp.scrap_cou_rep_can
        ptp.scrap_cou_ext_can += tp.scrap_cou_ext_can
        ptp.scrap_cou_rep_us += tp.scrap_cou_rep_us
        ptp.scrap_cou_ext_us += tp.scrap_cou_ext_us

        ptp.setup_qte += tp.setup_qte
        ptp.setup_cou_scr_can += tp.setup_cou_scr_can
        ptp.setup_cou_scr_us += tp.setup_cou_scr_us
        ptp.setup_budget += tp.setup_budget ' test

        ptp.vrac_qte += tp.vrac_qte
        ptp.vrac_cou_scr_can += tp.vrac_cou_scr_can
        ptp.vrac_cou_scr_us += tp.vrac_cou_scr_us

    End Sub

End Module
