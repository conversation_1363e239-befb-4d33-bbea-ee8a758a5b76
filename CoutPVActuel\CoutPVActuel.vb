﻿Imports UtilitiesData

Module CoutPVActuel

    Public Function CalculCoutPVActuel() As String ' retourne OK si tout est beau, ERROR sinon

        Try

            InitPEXRejet(ppdb)

            Dim palierMinimal = 1000
            Dim dat = Now

            ' Aller chercher les PV standards qui ont un produit existant.
            Dim lstPV = ppdb.Fetch(Of PV)("select pv.* from pv join produit prd on prd.id_produit = pv.id_produit where pv.id_client = 200251 order by pv.id_pv")
            Dim paliers = ppdb.Fetch(Of Integer)("select distinct nom from std_categorie_surcharge where @0 between date_debut and date_fin and nom >= @1 order by nom", Now, palierMinimal)
            Dim qte1st = paliers.First

            Dim lstCPA As New List(Of std_categorie_surcharge_cout_pv_actuel)

            For Each pv In lstPV
                Dim cpa As New std_categorie_surcharge_cout_pv_actuel
                lstCPA.Add(cpa)
                For Each qte In paliers
                    Dim crc = GetCoutRejet(pv.id_pv, dat, qte)
                    If qte = qte1st Then
                        cpa.id_produit = crc.id_produit
                        cpa.nb_couche = crc.nb_couches
                        cpa.id_std_categorie = crc.id_std_categorie
                        cpa.id_pv = crc.id_pv
                        cpa.taux = R4(crc.std_taux)
                        cpa.cout_pv_us = R4(crc.cout_pv_std)
                        cpa.id_resine_recyclage = crc.id_rsn_recyclage
                        cpa.cout_resine_recyclage_us = R4(crc.rsn_rcl_cout_us)
                        cpa.cout_extrusion_us = R4(crc.cout_extrusion_US)
                        cpa.cout_reprocess_us = R4(crc.cout_reprocess_US)
                        cpa.date_derniere_maj = dat
                    End If
                    Select Case qte
                        Case 1000
                            cpa.pourcentage_scrap_1000 = R4(crc.pourcentage_scrap)
                            cpa.cout_rejet_1000_us = R4(crc.cout_rejet_std_US_lb)
                        Case 2000
                            cpa.pourcentage_scrap_2000 = R4(crc.pourcentage_scrap)
                            cpa.cout_rejet_2000_us = R4(crc.cout_rejet_std_US_lb)
                        Case 5000
                            cpa.pourcentage_scrap_5000 = R4(crc.pourcentage_scrap)
                            cpa.cout_rejet_5000_us = R4(crc.cout_rejet_std_US_lb)
                        Case 10000
                            cpa.pourcentage_scrap_10000 = R4(crc.pourcentage_scrap)
                            cpa.cout_rejet_10000_us = R4(crc.cout_rejet_std_US_lb)
                        Case 20000
                            cpa.pourcentage_scrap_20000 = R4(crc.pourcentage_scrap)
                            cpa.cout_rejet_20000_us = R4(crc.cout_rejet_std_US_lb)
                        Case 40000
                            cpa.pourcentage_scrap_40000 = R4(crc.pourcentage_scrap)
                            cpa.cout_rejet_40000_us = R4(crc.cout_rejet_std_US_lb)
                        Case Else
                            Debug.Assert(False) 'todo email dev
                    End Select
                Next
            Next

#Region "Compare Data"
            ' Compare with existing, update only changes.
            Dim lstAct = ppdb.Fetch(Of std_categorie_surcharge_cout_pv_actuel)("")

            Dim lstUpdates As List(Of std_categorie_surcharge_cout_pv_actuel)
            Dim lstInsertIDs As New List(Of Integer)
            Dim lstDeleteIDs As New List(Of Integer)

            If lstAct.Count = 0 Then
                ' table est vide
                lstUpdates = lstCPA
            Else
                lstInsertIDs = lstCPA.Select(Function(x) x.id_pv).Except(lstAct.Select(Function(x) x.id_pv)).ToList ' new IDs to add
                lstDeleteIDs = lstAct.Select(Function(x) x.id_pv).Except(lstCPA.Select(Function(x) x.id_pv)).ToList ' old IDs to delete

                lstUpdates = New List(Of std_categorie_surcharge_cout_pv_actuel)

                For Each act In lstAct
                    Dim neo = lstCPA.Where(Function(x) x.id_pv = act.id_pv).SingleOrDefault ' on aura nothing si un PV a été supprimé. Traité via lstDelID ci-dessous; ne devrait pas se produire
                    If neo IsNot Nothing AndAlso Not act.Equals(neo) Then lstUpdates.Add(neo)
                Next
            End If

#End Region

#Region "Update Table"
            If lstUpdates.Any OrElse lstInsertIDs.Any OrElse lstDeleteIDs.Any Then
                Dim table = "STD_CATEGORIE_SURCHARGE_COUT_PV_ACTUEL"
                Using tx = ppdb.GetTransaction

                    ' Remove deleted and updated
                    If lstAct.Any Then ' si la liste des actuels est vide, on n'as pas de DELETE a faire...
                        lstDeleteIDs.AddRange(lstUpdates.Select(Function(x) x.id_pv))
                        If lstDeleteIDs.Any Then
                            Dim csv = String.Join(",", lstDeleteIDs)
                            Dim sql = $"delete from {table} where id_pv in ({csv})"
                            Dim recs = ppdb.Execute(sql)
                            Debug.Assert(recs = lstDeleteIDs.Count)
                        End If
                    End If

                    ' Insert updated and inserted
                    If lstInsertIDs.Any Then lstUpdates.AddRange(lstCPA.Where(Function(x) lstInsertIDs.Contains(x.id_pv)))
                    If lstUpdates.Any Then UtilitiesData.BulkCopy(lstUpdates, tx, table)

                    tx.Complete()
                End Using
            End If
#End Region

        Catch ex As Exception
            Return "ERROR"
        End Try

        Return "OK"

    End Function

    <PetaPoco.PrimaryKey("id_pv")>
    Private Class std_categorie_surcharge_cout_pv_actuel
        Implements IEquatable(Of std_categorie_surcharge_cout_pv_actuel)

        Public Property id_pv As Integer
        Public Property id_produit As Integer
        Public Property nb_couche As Integer
        Public Property id_std_categorie As Integer
        Public Property id_resine_recyclage As Integer
        Public Property taux As Decimal
        Public Property cout_pv_us As Decimal
        Public Property cout_resine_recyclage_us As Decimal
        Public Property cout_extrusion_us As Decimal
        Public Property cout_reprocess_us As Decimal
        Public Property pourcentage_scrap_1000 As Decimal
        Public Property pourcentage_scrap_2000 As Decimal
        Public Property pourcentage_scrap_5000 As Decimal
        Public Property pourcentage_scrap_10000 As Decimal
        Public Property pourcentage_scrap_20000 As Decimal
        Public Property pourcentage_scrap_40000 As Decimal
        Public Property cout_rejet_1000_us As Decimal
        Public Property cout_rejet_2000_us As Decimal
        Public Property cout_rejet_5000_us As Decimal
        Public Property cout_rejet_10000_us As Decimal
        Public Property cout_rejet_20000_us As Decimal
        Public Property cout_rejet_40000_us As Decimal
        Public Property date_derniere_maj As DateTime

        Public Overloads Function Equals(other As std_categorie_surcharge_cout_pv_actuel) As Boolean Implements IEquatable(Of std_categorie_surcharge_cout_pv_actuel).Equals

            ' on vérifie en ordre de plus susceptible de changer

            If _taux <> other.taux Then Return False

            If _cout_rejet_1000_us <> other.cout_rejet_1000_us Then Return False
            If _cout_rejet_2000_us <> other.cout_rejet_2000_us Then Return False
            If _cout_rejet_5000_us <> other.cout_rejet_5000_us Then Return False
            If _cout_rejet_10000_us <> other.cout_rejet_10000_us Then Return False
            If _cout_rejet_20000_us <> other.cout_rejet_20000_us Then Return False
            If _cout_rejet_40000_us <> other.cout_rejet_40000_us Then Return False

            If _pourcentage_scrap_1000 <> other.pourcentage_scrap_1000 Then Return False
            If _pourcentage_scrap_2000 <> other.pourcentage_scrap_2000 Then Return False
            If _pourcentage_scrap_5000 <> other.pourcentage_scrap_5000 Then Return False
            If _pourcentage_scrap_10000 <> other.pourcentage_scrap_10000 Then Return False
            If _pourcentage_scrap_20000 <> other.pourcentage_scrap_20000 Then Return False
            If _pourcentage_scrap_40000 <> other.pourcentage_scrap_40000 Then Return False

            If _cout_pv_us <> other.cout_pv_us Then Return False
            If _cout_resine_recyclage_us <> other.cout_resine_recyclage_us Then Return False
            If _cout_extrusion_us <> other.cout_extrusion_us Then Return False
            If _cout_reprocess_us <> other.cout_reprocess_us Then Return False

            If _id_pv <> other.id_pv Then Return False
            If _id_produit <> other.id_produit Then Return False
            If _nb_couche <> other.nb_couche Then Return False
            If _id_std_categorie <> other.id_std_categorie Then Return False
            If _id_resine_recyclage <> other.id_resine_recyclage Then Return False

            ' date_derniere_maj est ignorée

            Return True
        End Function

    End Class

End Module
