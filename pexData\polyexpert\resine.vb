﻿<PetaPoco.PrimaryKey("id_resine")>
Partial Public Class resine
    Public Property id_resine As Integer
    Public Property nom As String
    '	Public Property densite As Decimal?
    '	Public Property melt_index As Decimal?
    '	Public Property slip As String
    '	Public Property a_b As String
    '	Public Property id_fournisseur As Integer?
    '	Public Property type As String
    '	Public Property ppa As String
    '	Public Property commentaire As String
    '	Public Property densite_lbs_pied_cube As Decimal?
    '	Public Property note As Integer?
    '	Public Property prix_lbs_can As Decimal?
    '	Public Property prix_lbs_can_last As Decimal?
    '	Public Property prix_lbs_us As Decimal?
    '	Public Property prix_lbs_us_last As Decimal?
    '	Public Property prix_lbs_date As DateTime?
    '	Public Property prix_lbs_us_normalise As Decimal?
    '	Public Property prix_lbs_us_normalise_last As Decimal?
    '	Public Property prix_lbs_us_normalise_override As Boolean
    '	Public Property prix_lbs_norm_date As DateTime?
    '	Public Property taux_change As Decimal?
    '	Public Property qte_inventaire As Decimal?
    '	Public Property date_inventaire As DateTime?
    Public Property actif As Boolean
    '	Public Property resine_virtuelle As Boolean
    '	Public Property additif As Boolean
    '	Public Property actif_last As Boolean
    '	Public Property id_resine_relier As Integer?
    '	Public Property reactive As Boolean
    '	Public Property densite_bkp As Decimal?
    '	Public Property densite_cor As Decimal?
    '	Public Property date_creation As DateTime
    '	Public Property ancienne_ref As String
    '	Public Property resine_std As Boolean
    '	Public Property leadtime As Integer?
    '	Public Property id_std_resine As Integer?
    '	Public Property tmp_resine_type As Integer?
    '	Public Property resine_type_id As Integer?
    '	Public Property densite_reelle As Decimal?
    '	Public Property id_type_rejet_resine_densite As Integer?
    '	Public Property id_type_rejet_resine_couleur As Integer?
    '	Public Property id_type_rejet_dispo_restriction As Integer?
    '	Public Property compte_gl As String
    '	Public Property qte_inv_min_lbs As Integer?
    Public Property id_resine_groupe As Integer?
    '	Public Property is_usine_addable As Boolean
    '	Public Property isusinewatched As Boolean
    '	Public Property is_interne As Boolean
End Class
