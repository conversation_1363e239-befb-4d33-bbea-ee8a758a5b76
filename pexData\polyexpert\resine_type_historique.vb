﻿Imports ProtoBuf

<PetaPoco.PrimaryKey("ID_RESINE_TYPE_HISTORIQUE")>
<ProtoContract>
Partial Public Class resine_type_historique
    <ProtoMember(1)> Public Property id_resine_type_historique As Integer
    <ProtoMember(2)> Public Property id_resine_type As Integer
    <ProtoMember(3)> Public Property prix_lbs_us_normalise As Decimal
    <ProtoMember(4)> Public Property date_prix_lbs_us_normalise As DateTime
    <ProtoMember(5)> Public Property cout_us_cdi As Decimal
    <ProtoMember(6)> Public Property index_pe As Decimal
    <ProtoMember(7)> Public Property cout_us_std As Decimal
    <ProtoMember(8)> Public Property date_debut As DateTime
    <ProtoMember(9)> Public Property date_fin As DateTime
    <ProtoMember(10)> Public Property commentaire As String
End Class
